import Spinner from "@/components/Spinners/Spinner";
import { APIROUTES } from "@/helpers/apiRoutes";
import useFetchCurrentUser from "@/hooks/api/useFetchCurrentUser";
import { useQuery } from "@tanstack/react-query";
import { api } from "@/helpers/api";
import { useState } from "react";
import ListAuctionModal from "../features/auction/components/ListAuctionModal";
import MarketTable from "../features/auction/components/MarketTable";
import PurchaseItemModal from "../features/shop/components/PurchaseItemModal";

export default function Market() {
    const { data: auctionList, isLoading } = useQuery(api.auctions.auctionList.queryOptions());
    const [openCreateListingModal, setOpenCreateListingModal] = useState(false);
    const [openPurchaseItemModal, setOpenPurchaseItemModal] = useState(false);
    const [itemToSell, setItemToSell] = useState(null);
    const [itemToBuy, setItemToBuy] = useState(null);
    const { data: currentUser } = useFetchCurrentUser();

    return (
        <>
            <section className="mx-auto md:max-w-7xl md:rounded-lg md:py-6">
                <div className="mx-auto h-full px-0 md:px-6">
                    <div className="mx-auto h-full bg-gray-100 text-center md:rounded-lg md:border md:border-gray-700 dark:bg-slate-800 ">
                        {/* <div className="mb-4 mt-4">
              <h1 className="text-3xl font-medium text-custom-yellow tracking-tighter sm:text-4xl md:text-3xl">
                Market Stalls
              </h1>
              <p className=" text-gray-500 dark:text-gray-200 md:text-lg">
                Buy and sell items from other players
              </p>
            </div> */}
                        {/* {currentUser?.userType === "admin" && (
              <Button onClick={() => adminCreateListing()} variant="primary">
                List Item Admin
              </Button>
            )} */}

                        <div className="mt-4 md:mx-0">
                            {isLoading ? (
                                <Spinner center />
                            ) : (
                                <MarketTable
                                    auctionList={auctionList}
                                    setOpenModal={setOpenCreateListingModal}
                                    currentUser={currentUser}
                                    setItemToBuy={setItemToBuy}
                                    setOpenPurchaseItemModal={setOpenPurchaseItemModal}
                                />
                            )}
                        </div>
                        <ListAuctionModal
                            openModal={openCreateListingModal}
                            setOpenModal={setOpenCreateListingModal}
                            itemToSell={itemToSell}
                            setItemToSell={setItemToSell}
                            currentUser={currentUser}
                        />
                        {itemToBuy && (
                            <div className="mt-4 md:mx-0">
                                <PurchaseItemModal
                                    openModal={openPurchaseItemModal}
                                    setOpenModal={setOpenPurchaseItemModal}
                                    itemToBuy={itemToBuy}
                                />
                            </div>
                        )}
                    </div>
                </div>
            </section>
        </>
    );
}
