import{b,c as i,e as p,g as h,an as n,y as f,j as e,r as u,B as N,t as k,C as j}from"./index-DOR_A_Wy.js";import{C as v}from"./Callout-BJ8W9BH3.js";import{U as w}from"./UsersTable-Rq_HKNZs.js";const B=r=>b(i.bounties.activeBountyList.queryOptions({staleTime:3e4,enabled:r?.enabled!==!1})),C=r=>{const t=p();return h(i.bounties.placeBounty.mutationOptions({onSuccess:()=>{t.invalidateQueries({queryKey:i.bounties.activeBountyList.key()}),t.invalidateQueries({queryKey:f.USER.CURRENTUSERINFO}),n.success("Bounty placed successfully!"),r&&r()},onError:a=>{const o=a.message||"Failed to place bounty";console.error(o),n.error(o)}}))};function U(){const{data:r,isLoading:t,error:a}=B();return a?"An error has occurred: "+a.message:e.jsxs("div",{className:"mb-8 pb-4 md:mx-auto md:mb-0 md:max-w-6xl md:pb-0",children:[e.jsx(v,{className:"mt-3 mb-4",title:"Here you will find students that have been placed on the bounty list.",children:e.jsx("p",{className:"text-gray-300!",children:"You must defeat the student in a PvP battle and choose to 'Cripple' them in order to claim the bounty reward!"})}),e.jsx(E,{}),e.jsx(w,{data:r,isLoading:t,type:"bounties"})]})}const E=()=>{const[r,t]=u.useState(null),[a,o]=u.useState(null),[l,c]=u.useState(null),{MIN_BOUNTY:d,BOUNTY_FEE:m}=N(),{data:g}=k(),x=C(()=>{t(null),o(null),c(null)}),y=()=>{if(r===null){n.error("Enter a valid Student ID");return}if(a===null){n.error("Enter a reward amount");return}if(l===null){n.error("Enter a bounty reason");return}if(a<d){n.error(`Bounty rewards must be at least ¥${d}`);return}const s=m+1;if(a*s>g?.cash){n.error("You need more cash to place this bounty!");return}x.mutate({amount:Number.parseInt(a),targetId:Number.parseInt(r),reason:l})};return e.jsxs("div",{className:"mb-4 grid h-36 grid-cols-6 grid-rows-2 gap-x-4 gap-y-2 px-4 md:flex md:h-16 md:gap-5 md:px-0",children:[e.jsxs("div",{className:"col-span-3 md:w-[10%]",children:[e.jsxs("label",{className:"mb-2 block text-gray-700 text-xs uppercase tracking-wide dark:text-gray-200",htmlFor:"studentid",children:["Student ID",e.jsx("span",{className:"text-red-500",children:" *"})]}),e.jsxs("div",{className:"mt-1 flex rounded-md shadow-xs",children:[e.jsx("span",{className:"inline-flex items-center rounded-l-md border border-gray-300 border-r-0 bg-gray-50 px-3 text-gray-500 sm:text-sm dark:border-gray-600 dark:bg-gray-700 dark:text-gray-200",children:"#"}),e.jsx("input",{type:"number",name:"studentid",min:1,id:"studentid",value:r||"",className:"block w-full min-w-0 flex-1 rounded-none rounded-r-md border-gray-300 px-3 py-2 focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm dark:border-gray-600 dark:bg-gray-800 dark:text-gray-200",placeholder:"1",onChange:s=>{t(s.target.value)}})]})]}),e.jsxs("div",{className:"col-span-3 md:w-1/6",children:[e.jsxs("label",{className:"mb-2 block text-gray-700 text-xs uppercase tracking-wide dark:text-gray-200",htmlFor:"amount",children:["Reward",e.jsx("span",{className:"text-red-500",children:" *"})," ",e.jsxs("span",{className:"text-gray-400",children:["(",m*100,"% fee)"]})]}),e.jsxs("div",{className:"mt-1 flex rounded-md shadow-xs",children:[e.jsx("span",{className:"inline-flex items-center rounded-l-md border border-gray-300 border-r-0 bg-gray-50 px-3 text-gray-500 sm:text-sm dark:border-gray-600 dark:bg-gray-700 dark:text-gray-200",children:"¥"}),e.jsx("input",{type:"number",name:"amount",id:"amount",value:a||"",min:d,className:"block w-full min-w-0 flex-1 rounded-none rounded-r-md border-gray-300 px-3 py-2 focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm dark:border-gray-600 dark:bg-gray-800 dark:text-gray-200",placeholder:d,onChange:s=>{o(parseInt(s.target.value))}})]})]}),e.jsxs("div",{className:"col-span-4 md:w-2/6",children:[e.jsxs("label",{className:"mb-2 block text-gray-700 text-xs uppercase tracking-wide dark:text-gray-200",htmlFor:"reason",children:["Reason",e.jsx("span",{className:"text-red-500",children:" *"})]}),e.jsx("div",{className:"mt-1 flex rounded-md shadow-xs",children:e.jsx("input",{type:"text",name:"reason",id:"reason",value:l||"",className:"block w-full min-w-0 flex-1 rounded-md border-gray-300 px-3 py-2 focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm dark:border-gray-600 dark:bg-gray-800 dark:text-gray-200",placeholder:"No Reason",onChange:s=>{c(s.target.value)}})})]}),e.jsx(j,{isLoading:x.isPending,variant:"flat",className:"col-span-2 mt-auto",textSize:"text-xs md:text-sm",onClick:()=>y(),children:"Place Bounty"})]})};export{U as default};
