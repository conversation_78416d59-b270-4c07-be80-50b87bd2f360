import { APIROUTES } from "@/helpers/apiRoutes";
import { api } from "@/helpers/api";
import { useMutation, useQueryClient } from "@tanstack/react-query";

export const useBeginRooftopBattle = () => {
    const queryClient = useQueryClient();

    return useMutation(
        api.battle.battleBeginRooftopBattle.mutationOptions({
            onSuccess: () => {
                queryClient.invalidateQueries({ queryKey: api.battle.status.key() });
                queryClient.invalidateQueries({ queryKey: api.rooftop.npcList.key() });
                queryClient.invalidateQueries({ queryKey: APIROUTES.USER.CURRENTUSERINFO });
            },
        })
    );
};

export default useBeginRooftopBattle;
