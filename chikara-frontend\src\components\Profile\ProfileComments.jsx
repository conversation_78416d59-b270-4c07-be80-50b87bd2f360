import Button from "@/components/Buttons/Button";
import { api } from "@/helpers/api";
import { useMutation, useQuery, useQueryClient } from "@tanstack/react-query";
import { Fragment, useState } from "react";
import toast from "react-hot-toast";
import SingleComment from "./SingleComment";

import { DisplayAvatar } from "@/components/DisplayAvatar";
import useFetchCurrentUser from "@/hooks/api/useFetchCurrentUser";

export default function ProfileComments({ userID }) {
    const { data: currentUser } = useFetchCurrentUser();
    const { data: profileComments } = useQuery(
        api.profile.viewComments.queryOptions({
            input: { userId: Number.parseInt(userID) },
        })
    );
    const [message, setMessage] = useState("");
    const queryClient = useQueryClient();

    const handleSubmit = (e) => {
        e.preventDefault();
        if (message === "") {
            toast.error("Please enter a profile comment!");
            return;
        }
        if (message.length < 5) {
            toast.error("Please enter a minimum of 5 characters!");
            return;
        }
        const parsedID = parseInt(userID);
        if (parsedID === currentUser.id) {
            toast.error("You can't comment on your own profile!");
            return;
        }

        addProfileComment.mutate({
            userId: parsedID,
            message: message,
        });
    };

    const handleInputChange = (e) => {
        const inputText = e.target.value;
        const lines = inputText.split("\n");
        let adjustedText = inputText;

        if (lines.length > 10) {
            // Limit the number of lines
            adjustedText = lines.slice(0, 10).join("\n");
        } else if (inputText.length > 140) {
            // Limit the number of characters
            adjustedText = inputText.substring(0, 140);
        }

        setMessage(adjustedText);
    };

    const addProfileComment = useMutation(
        api.profile.createComment.mutationOptions({
            onSuccess: () => {
                queryClient.invalidateQueries({
                    queryKey: api.profile.viewComments.queryKey({
                        input: { userId: Number.parseInt(userID) },
                    }),
                });
                setMessage("");
                toast.success("Profile comment added!");
            },
            onError: (error) => {
                const errorMessage = error.message || "Unknown error occurred";
                console.error(errorMessage);
                toast.error(errorMessage);
            },
        })
    );

    return (
        <section aria-labelledby="notes-title">
            <div className="rounded-lg bg-white shadow-sm sm:overflow-hidden dark:bg-gray-800">
                <div className="divide-y divide-gray-200 dark:divide-gray-600">
                    <div className="px-4 py-5 sm:px-6">
                        <h2 id="notes-title" className="font-medium text-gray-900 text-lg dark:text-gray-200">
                            Profile Comments
                        </h2>
                    </div>
                    <div className="px-4 py-6 sm:px-6">
                        <ul className="space-y-8">
                            {profileComments?.map((comment, i) => (
                                <Fragment key={i}>
                                    <SingleComment comment={comment} />
                                </Fragment>
                            ))}
                            {profileComments?.length < 1 && (
                                <p className="text-center text-xl dark:text-gray-200">No comments here yet!</p>
                            )}
                        </ul>
                    </div>
                </div>
                <div className="rounded-b-lg bg-gray-50 px-4 py-6 sm:px-6 dark:bg-gray-800">
                    <div className="flex space-x-3">
                        <div className="shrink-0">
                            <DisplayAvatar className="size-10 rounded-full" src={currentUser} />
                        </div>
                        <div className="min-w-0 flex-1">
                            <form onSubmit={handleSubmit}>
                                <div>
                                    <textarea
                                        id="comment"
                                        name="comment"
                                        rows={3}
                                        maxLength="140"
                                        className="block w-full rounded-md border border-gray-300 bg-white shadow-xs focus:border-blue-500 focus:ring-blue-500 sm:text-sm dark:border-gray-500 dark:bg-gray-700 dark:text-slate-200 dark:text-stroke-sm dark:placeholder:text-gray-300"
                                        placeholder="Add a comment.."
                                        value={message}
                                        onChange={handleInputChange}
                                    />
                                </div>
                                <div className="mt-3 flex items-center justify-end">
                                    <Button type="primary">Comment</Button>
                                </div>
                            </form>
                        </div>
                    </div>
                </div>
            </div>
        </section>
    );
}
