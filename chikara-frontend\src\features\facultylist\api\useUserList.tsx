import { api } from "@/helpers/api";
import { keepPreviousData, useQuery } from "@tanstack/react-query";

/**
 * Hook for fetching the list of faculty members
 * @returns Query result with faculty list data
 */
const useUserList = () => {
    return useQuery(
        api.user.facultyList.queryOptions({
            staleTime: 5 * 60 * 1000, // 5 minutes
            placeholderData: keepPreviousData,
        })
    );
};

export default useUserList;
