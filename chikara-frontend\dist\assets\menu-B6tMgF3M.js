import{r as u,aP as $,m as T,aQ as R,aR as ge,aS as L,aT as F,aU as O,aV as Ae,aW as le,aX as _e,aY as Ne,aZ as he,a_ as Ie,a$ as se,b0 as Le,b1 as Ge,b2 as ye,b3 as Be,b4 as oe,b5 as Se,b6 as je,b7 as xe,b8 as Ue,b9 as Qe,ba as k,bb as He,bc as Ve,bd as Ke,be as K,bf as Ye,bg as qe,bh as We,bi as ae,bj as ze,bk as Xe,bl as Ze,bm as Je,bn as et,bo as Ee,bp as tt,bq as nt,br as rt,bs as lt,bt as st,bu as ot,bv as at,bw as ut,bx as it,by as ue,bz as w,bA as W,bB as ct,bC as ie,bD as $e,bE as dt,bF as pt,bG as ft}from"./index-DOR_A_Wy.js";let mt=u.createContext(void 0);function vt(){return u.useContext(mt)}let X=u.createContext(null);X.displayName="DescriptionContext";function Me(){let e=u.useContext(X);if(e===null){let n=new Error("You used a <Description /> component, but it is not inside a relevant parent.");throw Error.captureStackTrace&&Error.captureStackTrace(n,Me),n}return e}function bt(){let[e,n]=u.useState([]);return[e.length>0?e.join(" "):void 0,u.useMemo(()=>function(t){let l=$(r=>(n(o=>[...o,r]),()=>n(o=>{let i=o.slice(),d=i.indexOf(r);return d!==-1&&i.splice(d,1),i}))),s=u.useMemo(()=>({register:l,slot:t.slot,name:t.name,props:t.props,value:t.value}),[l,t.slot,t.name,t.props,t.value]);return T.createElement(X.Provider,{value:s},t.children)},[n])]}let gt="p";function ht(e,n){let t=u.useId(),l=ge(),{id:s=`headlessui-description-${t}`,...r}=e,o=Me(),i=L(n);F(()=>o.register(s),[s,o.register]);let d=l||!1,b=u.useMemo(()=>({...o.slot,disabled:d}),[o.slot,d]),a={ref:i,...o.props,id:s};return O()({ourProps:a,theirProps:r,slot:b,defaultTag:gt,name:o.name||"Description"})}let It=R(ht);Object.assign(It,{});let q=u.createContext(null);q.displayName="LabelContext";function Z(){let e=u.useContext(q);if(e===null){let n=new Error("You used a <Label /> component, but it is not inside a relevant parent.");throw Error.captureStackTrace&&Error.captureStackTrace(n,Z),n}return e}function yt(e){var n,t,l;let s=(t=(n=u.useContext(q))==null?void 0:n.value)!=null?t:void 0;return((l=void 0)!=null?l:0)>0?[s,...e].filter(Boolean).join(" "):s}function Ce({inherit:e=!1}={}){let n=yt(),[t,l]=u.useState([]),s=e?[n,...t].filter(Boolean):t;return[s.length>0?s.join(" "):void 0,u.useMemo(()=>function(r){let o=$(d=>(l(b=>[...b,d]),()=>l(b=>{let a=b.slice(),y=a.indexOf(d);return y!==-1&&a.splice(y,1),a}))),i=u.useMemo(()=>({register:o,slot:r.slot,name:r.name,props:r.props,value:r.value}),[o,r.slot,r.name,r.props,r.value]);return T.createElement(q.Provider,{value:i},r.children)},[l])]}let St="label";function xt(e,n){var t;let l=u.useId(),s=Z(),r=vt(),o=ge(),{id:i=`headlessui-label-${l}`,htmlFor:d=r??((t=s.props)==null?void 0:t.htmlFor),passive:b=!1,...a}=e,y=L(n);F(()=>s.register(i),[i,s.register]);let g=$(I=>{let S=I.currentTarget;if(!(I.target!==I.currentTarget&&Ae(I.target))&&(le(S)&&I.preventDefault(),s.props&&"onClick"in s.props&&typeof s.props.onClick=="function"&&s.props.onClick(I),le(S))){let m=document.getElementById(S.htmlFor);if(m){let D=m.getAttribute("disabled");if(D==="true"||D==="")return;let P=m.getAttribute("aria-disabled");if(P==="true"||P==="")return;(_e(m)&&(m.type==="file"||m.type==="radio"||m.type==="checkbox")||m.role==="radio"||m.role==="checkbox"||m.role==="switch")&&m.click(),m.focus({preventScroll:!0})}}}),p=o||!1,E=u.useMemo(()=>({...s.slot,disabled:p}),[s.slot,p]),C={ref:y,...s.props,id:i,htmlFor:d,onClick:g};return b&&("onClick"in C&&(delete C.htmlFor,delete C.onClick),"onClick"in a&&delete a.onClick),O()({ourProps:C,theirProps:a,slot:E,defaultTag:d?St:"div",name:s.name||"Label"})}let Et=R(xt);Object.assign(Et,{});let z=new Map,U=new Map;function ce(e){var n;let t=(n=U.get(e))!=null?n:0;return U.set(e,t+1),t!==0?()=>de(e):(z.set(e,{"aria-hidden":e.getAttribute("aria-hidden"),inert:e.inert}),e.setAttribute("aria-hidden","true"),e.inert=!0,()=>de(e))}function de(e){var n;let t=(n=U.get(e))!=null?n:1;if(t===1?U.delete(e):U.set(e,t-1),t!==1)return;let l=z.get(e);l&&(l["aria-hidden"]===null?e.removeAttribute("aria-hidden"):e.setAttribute("aria-hidden",l["aria-hidden"]),e.inert=l.inert,z.delete(e))}function $t(e,{allowed:n,disallowed:t}={}){let l=Ne(e,"inert-others");F(()=>{var s,r;if(!l)return;let o=he();for(let d of(s=t?.())!=null?s:[])d&&o.add(ce(d));let i=(r=n?.())!=null?r:[];for(let d of i){if(!d)continue;let b=Ie(d);if(!b)continue;let a=d.parentElement;for(;a&&a!==b.body;){for(let y of a.children)i.some(g=>y.contains(g))||o.add(ce(y));a=a.parentElement}}return o.dispose},[l,n,t])}var Mt=(e=>(e[e.Ignore=0]="Ignore",e[e.Select=1]="Select",e[e.Close=2]="Close",e))(Mt||{});const H={Ignore:{kind:0},Select:e=>({kind:1,target:e}),Close:{kind:2}},Ct=200;function wt(e,{trigger:n,action:t,close:l,select:s}){let r=u.useRef(null);se(e&&n!==null,"pointerdown",o=>{Le(o?.target)&&n!=null&&n.contains(o.target)&&(r.current=new Date)}),se(e&&n!==null,"pointerup",o=>{if(r.current===null||!Ge(o.target))return;let i=t(o),d=new Date().getTime()-r.current.getTime();switch(r.current=null,i.kind){case 0:return;case 1:{d>Ct&&(s(i.target),l());break}case 2:{l();break}}},{capture:!0})}function pe(e){return[e.screenX,e.screenY]}function Pt(){let e=u.useRef([-1,-1]);return{wasMoved(n){let t=pe(n);return e.current[0]===t[0]&&e.current[1]===t[1]?!1:(e.current=t,!0)},update(n){e.current=pe(n)}}}function Dt(e,{container:n,accept:t,walk:l}){let s=u.useRef(t),r=u.useRef(l);u.useEffect(()=>{s.current=t,r.current=l},[t,l]),F(()=>{if(!n||!e)return;let o=Ie(n);if(!o)return;let i=s.current,d=r.current,b=Object.assign(y=>i(y),{acceptNode:i}),a=o.createTreeWalker(n,NodeFilter.SHOW_ELEMENT,b,!1);for(;a.nextNode();)d(a.currentNode)},[n,e,s,r])}function kt(e){throw new Error("Unexpected object: "+e)}var x=(e=>(e[e.First=0]="First",e[e.Previous=1]="Previous",e[e.Next=2]="Next",e[e.Last=3]="Last",e[e.Specific=4]="Specific",e[e.Nothing=5]="Nothing",e))(x||{});function V(e,n){let t=n.resolveItems();if(t.length<=0)return null;let l=n.resolveActiveIndex(),s=l??-1;switch(e.focus){case 0:{for(let r=0;r<t.length;++r)if(!n.resolveDisabled(t[r],r,t))return r;return l}case 1:{s===-1&&(s=t.length);for(let r=s-1;r>=0;--r)if(!n.resolveDisabled(t[r],r,t))return r;return l}case 2:{for(let r=s+1;r<t.length;++r)if(!n.resolveDisabled(t[r],r,t))return r;return l}case 3:{for(let r=t.length-1;r>=0;--r)if(!n.resolveDisabled(t[r],r,t))return r;return l}case 4:{for(let r=0;r<t.length;++r)if(n.resolveId(t[r],r,t)===e.id)return r;return l}case 5:return null;default:kt(e)}}function Tt(e,n){let t=u.useRef({left:0,top:0});if(F(()=>{if(!n)return;let s=n.getBoundingClientRect();s&&(t.current=s)},[e,n]),n==null||!e||n===document.activeElement)return!1;let l=n.getBoundingClientRect();return l.top!==t.current.top||l.left!==t.current.left}let fe=/([\u2700-\u27BF]|[\uE000-\uF8FF]|\uD83C[\uDC00-\uDFFF]|\uD83D[\uDC00-\uDFFF]|[\u2011-\u26FF]|\uD83E[\uDD10-\uDDFF])/g;function me(e){var n,t;let l=(n=e.innerText)!=null?n:"",s=e.cloneNode(!0);if(!ye(s))return l;let r=!1;for(let i of s.querySelectorAll('[hidden],[aria-hidden],[role="img"]'))i.remove(),r=!0;let o=r?(t=s.innerText)!=null?t:"":l;return fe.test(o)&&(o=o.replace(fe,"")),o}function Ft(e){let n=e.getAttribute("aria-label");if(typeof n=="string")return n.trim();let t=e.getAttribute("aria-labelledby");if(t){let l=t.split(" ").map(s=>{let r=document.getElementById(s);if(r){let o=r.getAttribute("aria-label");return typeof o=="string"?o.trim():me(r).trim()}return null}).filter(Boolean);if(l.length>0)return l.join(", ")}return me(e).trim()}function Rt(e){let n=u.useRef(""),t=u.useRef("");return $(()=>{let l=e.current;if(!l)return"";let s=l.innerText;if(n.current===s)return t.current;let r=Ft(l).trim().toLowerCase();return n.current=s,t.current=r,r})}var Ot=Object.defineProperty,At=(e,n,t)=>n in e?Ot(e,n,{enumerable:!0,configurable:!0,writable:!0,value:t}):e[n]=t,ve=(e,n,t)=>(At(e,typeof n!="symbol"?n+"":n,t),t),M=(e=>(e[e.Open=0]="Open",e[e.Closed=1]="Closed",e))(M||{}),Y=(e=>(e[e.Pointer=0]="Pointer",e[e.Other=1]="Other",e))(Y||{}),v=(e=>(e[e.OpenMenu=0]="OpenMenu",e[e.CloseMenu=1]="CloseMenu",e[e.GoToItem=2]="GoToItem",e[e.Search=3]="Search",e[e.ClearSearch=4]="ClearSearch",e[e.RegisterItems=5]="RegisterItems",e[e.UnregisterItems=6]="UnregisterItems",e[e.SetButtonElement=7]="SetButtonElement",e[e.SetItemsElement=8]="SetItemsElement",e[e.SortItems=9]="SortItems",e))(v||{});function be(e,n=t=>t){let t=e.activeItemIndex!==null?e.items[e.activeItemIndex]:null,l=Ue(n(e.items.slice()),r=>r.dataRef.current.domRef.current),s=t?l.indexOf(t):null;return s===-1&&(s=null),{items:l,activeItemIndex:s}}let _t={1(e){return e.menuState===1?e:{...e,activeItemIndex:null,pendingFocus:{focus:x.Nothing},menuState:1}},0(e,n){return e.menuState===0?e:{...e,__demoMode:!1,pendingFocus:n.focus,menuState:0}},2:(e,n)=>{var t,l,s,r,o;if(e.menuState===1)return e;let i={...e,searchQuery:"",activationTrigger:(t=n.trigger)!=null?t:1,__demoMode:!1};if(n.focus===x.Nothing)return{...i,activeItemIndex:null};if(n.focus===x.Specific)return{...i,activeItemIndex:e.items.findIndex(a=>a.id===n.id)};if(n.focus===x.Previous){let a=e.activeItemIndex;if(a!==null){let y=e.items[a].dataRef.current.domRef,g=V(n,{resolveItems:()=>e.items,resolveActiveIndex:()=>e.activeItemIndex,resolveId:p=>p.id,resolveDisabled:p=>p.dataRef.current.disabled});if(g!==null){let p=e.items[g].dataRef.current.domRef;if(((l=y.current)==null?void 0:l.previousElementSibling)===p.current||((s=p.current)==null?void 0:s.previousElementSibling)===null)return{...i,activeItemIndex:g}}}}else if(n.focus===x.Next){let a=e.activeItemIndex;if(a!==null){let y=e.items[a].dataRef.current.domRef,g=V(n,{resolveItems:()=>e.items,resolveActiveIndex:()=>e.activeItemIndex,resolveId:p=>p.id,resolveDisabled:p=>p.dataRef.current.disabled});if(g!==null){let p=e.items[g].dataRef.current.domRef;if(((r=y.current)==null?void 0:r.nextElementSibling)===p.current||((o=p.current)==null?void 0:o.nextElementSibling)===null)return{...i,activeItemIndex:g}}}}let d=be(e),b=V(n,{resolveItems:()=>d.items,resolveActiveIndex:()=>d.activeItemIndex,resolveId:a=>a.id,resolveDisabled:a=>a.dataRef.current.disabled});return{...i,...d,activeItemIndex:b}},3:(e,n)=>{let t=e.searchQuery!==""?0:1,l=e.searchQuery+n.value.toLowerCase(),s=(e.activeItemIndex!==null?e.items.slice(e.activeItemIndex+t).concat(e.items.slice(0,e.activeItemIndex+t)):e.items).find(o=>{var i;return((i=o.dataRef.current.textValue)==null?void 0:i.startsWith(l))&&!o.dataRef.current.disabled}),r=s?e.items.indexOf(s):-1;return r===-1||r===e.activeItemIndex?{...e,searchQuery:l}:{...e,searchQuery:l,activeItemIndex:r,activationTrigger:1}},4(e){return e.searchQuery===""?e:{...e,searchQuery:"",searchActiveItemIndex:null}},5:(e,n)=>{let t=e.items.concat(n.items.map(s=>s)),l=e.activeItemIndex;return e.pendingFocus.focus!==x.Nothing&&(l=V(e.pendingFocus,{resolveItems:()=>t,resolveActiveIndex:()=>e.activeItemIndex,resolveId:s=>s.id,resolveDisabled:s=>s.dataRef.current.disabled})),{...e,items:t,activeItemIndex:l,pendingFocus:{focus:x.Nothing},pendingShouldSort:!0}},6:(e,n)=>{let t=e.items,l=[],s=new Set(n.items);for(let[r,o]of t.entries())if(s.has(o.id)&&(l.push(r),s.delete(o.id),s.size===0))break;if(l.length>0){t=t.slice();for(let r of l.reverse())t.splice(r,1)}return{...e,items:t,activationTrigger:1}},7:(e,n)=>e.buttonElement===n.element?e:{...e,buttonElement:n.element},8:(e,n)=>e.itemsElement===n.element?e:{...e,itemsElement:n.element},9:e=>e.pendingShouldSort?{...e,...be(e),pendingShouldSort:!1}:e};class J extends Be{constructor(n){super(n),ve(this,"actions",{registerItem:oe(()=>{let t=[],l=new Set;return[(s,r)=>{l.has(r)||(l.add(r),t.push({id:s,dataRef:r}))},()=>(l.clear(),this.send({type:5,items:t.splice(0)}))]}),unregisterItem:oe(()=>{let t=[];return[l=>t.push(l),()=>this.send({type:6,items:t.splice(0)})]})}),ve(this,"selectors",{activeDescendantId(t){var l;let s=t.activeItemIndex,r=t.items;return s===null||(l=r[s])==null?void 0:l.id},isActive(t,l){var s;let r=t.activeItemIndex,o=t.items;return r!==null?((s=o[r])==null?void 0:s.id)===l:!1},shouldScrollIntoView(t,l){return t.__demoMode||t.menuState!==0||t.activationTrigger===0?!1:this.isActive(t,l)}}),this.on(5,()=>{this.disposables.requestAnimationFrame(()=>{this.send({type:9})})});{let t=this.state.id,l=Se.get(null);this.disposables.add(l.on(je.Push,s=>{!l.selectors.isTop(s,t)&&this.state.menuState===0&&this.send({type:1})})),this.on(0,()=>l.actions.push(t)),this.on(1,()=>l.actions.pop(t))}}static new({id:n,__demoMode:t=!1}){return new J({id:n,__demoMode:t,menuState:t?0:1,buttonElement:null,itemsElement:null,items:[],searchQuery:"",activeItemIndex:null,activationTrigger:1,pendingShouldSort:!1,pendingFocus:{focus:x.Nothing}})}reduce(n,t){return xe(t.type,_t,n,t)}}const we=u.createContext(null);function ee(e){let n=u.useContext(we);if(n===null){let t=new Error(`<${e} /> is missing a parent <Menu /> component.`);throw Error.captureStackTrace&&Error.captureStackTrace(t,Pe),t}return n}function Pe({id:e,__demoMode:n=!1}){let t=u.useMemo(()=>J.new({id:e,__demoMode:n}),[]);return Qe(()=>t.dispose()),t}let Nt=u.Fragment;function Lt(e,n){let t=u.useId(),{__demoMode:l=!1,...s}=e,r=Pe({id:t,__demoMode:l}),[o,i,d]=k(r,I=>[I.menuState,I.itemsElement,I.buttonElement]),b=L(n),a=Se.get(null),y=k(a,u.useCallback(I=>a.selectors.isTop(I,t),[a,t]));He(y,[d,i],(I,S)=>{var m;r.send({type:v.CloseMenu}),pt(S,ft.Loose)||(I.preventDefault(),(m=r.state.buttonElement)==null||m.focus())});let g=$(()=>{r.send({type:v.CloseMenu})}),p=u.useMemo(()=>({open:o===M.Open,close:g}),[o,g]),E={ref:b},C=O();return T.createElement(Ve,null,T.createElement(we.Provider,{value:r},T.createElement(Ke,{value:xe(o,{[M.Open]:K.Open,[M.Closed]:K.Closed})},C({ourProps:E,theirProps:s,slot:p,defaultTag:Nt,name:"Menu"}))))}let Gt="button";function Bt(e,n){let t=ee("Menu.Button"),l=u.useId(),{id:s=`headlessui-menu-button-${l}`,disabled:r=!1,autoFocus:o=!1,...i}=e,d=u.useRef(null),b=lt(),a=L(n,d,st(),$(f=>t.send({type:v.SetButtonElement,element:f}))),y=$(f=>{switch(f.key){case w.Space:case w.Enter:case w.ArrowDown:f.preventDefault(),f.stopPropagation(),t.send({type:v.OpenMenu,focus:{focus:x.First}});break;case w.ArrowUp:f.preventDefault(),f.stopPropagation(),t.send({type:v.OpenMenu,focus:{focus:x.Last}});break}}),g=$(f=>{switch(f.key){case w.Space:f.preventDefault();break}}),[p,E,C]=k(t,f=>[f.menuState,f.buttonElement,f.itemsElement]),I=p===M.Open;wt(I,{trigger:E,action:u.useCallback(f=>{if(E!=null&&E.contains(f.target))return H.Ignore;let h=f.target.closest('[role="menuitem"]:not([data-disabled])');return ye(h)?H.Select(h):C!=null&&C.contains(f.target)?H.Ignore:H.Close},[E,C]),close:u.useCallback(()=>t.send({type:v.CloseMenu}),[]),select:u.useCallback(f=>f.click(),[])});let S=$(f=>{var h;if(f.button===0){if(dt(f.currentTarget))return f.preventDefault();r||(p===M.Open?(W.flushSync(()=>t.send({type:v.CloseMenu})),(h=d.current)==null||h.focus({preventScroll:!0})):(f.preventDefault(),t.send({type:v.OpenMenu,focus:{focus:x.Nothing},trigger:Y.Pointer})))}}),{isFocusVisible:m,focusProps:D}=ot({autoFocus:o}),{isHovered:P,hoverProps:_}=at({isDisabled:r}),{pressed:A,pressProps:G}=ut({disabled:r}),N=u.useMemo(()=>({open:p===M.Open,active:A||p===M.Open,disabled:r,hover:P,focus:m,autofocus:o}),[p,P,m,A,r,o]),B=Ee(b(),{ref:a,id:s,type:it(e,d.current),"aria-haspopup":"menu","aria-controls":C?.id,"aria-expanded":p===M.Open,disabled:r||void 0,autoFocus:o,onKeyDown:y,onKeyUp:g,onPointerDown:S},D,_,G);return O()({ourProps:B,theirProps:i,slot:N,defaultTag:Gt,name:"Menu.Button"})}let jt="div",Ut=ue.RenderStrategy|ue.Static;function Qt(e,n){let t=u.useId(),{id:l=`headlessui-menu-items-${t}`,anchor:s,portal:r=!1,modal:o=!0,transition:i=!1,...d}=e,b=Ye(s),a=ee("Menu.Items"),[y,g]=qe(b),p=We(),[E,C]=u.useState(null),I=L(n,b?y:null,$(c=>a.send({type:v.SetItemsElement,element:c})),C),[S,m]=k(a,c=>[c.menuState,c.buttonElement]),D=ae(m),P=ae(E);b&&(r=!0);let _=ze(),[A,G]=Xe(i,E,_!==null?(_&K.Open)===K.Open:S===M.Open);Ze(A,m,()=>{a.send({type:v.CloseMenu})});let N=k(a,c=>c.__demoMode),B=N?!1:o&&S===M.Open;Je(B,P);let f=N?!1:o&&S===M.Open;$t(f,{allowed:u.useCallback(()=>[m,E],[m,E])});let h=S!==M.Open,Q=Tt(h,m)?!1:A;u.useEffect(()=>{let c=E;c&&S===M.Open&&c!==P?.activeElement&&c.focus({preventScroll:!0})},[S,E,P]),Dt(S===M.Open,{container:E,accept(c){return c.getAttribute("role")==="menuitem"?NodeFilter.FILTER_REJECT:c.hasAttribute("role")?NodeFilter.FILTER_SKIP:NodeFilter.FILTER_ACCEPT},walk(c){c.setAttribute("role","none")}});let te=et(),De=$(c=>{var j,ne,re;switch(te.dispose(),c.key){case w.Space:if(a.state.searchQuery!=="")return c.preventDefault(),c.stopPropagation(),a.send({type:v.Search,value:c.key});case w.Enter:if(c.preventDefault(),c.stopPropagation(),a.state.activeItemIndex!==null){let{dataRef:Oe}=a.state.items[a.state.activeItemIndex];(ne=(j=Oe.current)==null?void 0:j.domRef.current)==null||ne.click()}a.send({type:v.CloseMenu}),$e(a.state.buttonElement);break;case w.ArrowDown:return c.preventDefault(),c.stopPropagation(),a.send({type:v.GoToItem,focus:x.Next});case w.ArrowUp:return c.preventDefault(),c.stopPropagation(),a.send({type:v.GoToItem,focus:x.Previous});case w.Home:case w.PageUp:return c.preventDefault(),c.stopPropagation(),a.send({type:v.GoToItem,focus:x.First});case w.End:case w.PageDown:return c.preventDefault(),c.stopPropagation(),a.send({type:v.GoToItem,focus:x.Last});case w.Escape:c.preventDefault(),c.stopPropagation(),W.flushSync(()=>a.send({type:v.CloseMenu})),(re=a.state.buttonElement)==null||re.focus({preventScroll:!0});break;case w.Tab:c.preventDefault(),c.stopPropagation(),W.flushSync(()=>a.send({type:v.CloseMenu})),ct(a.state.buttonElement,c.shiftKey?ie.Previous:ie.Next);break;default:c.key.length===1&&(a.send({type:v.Search,value:c.key}),te.setTimeout(()=>a.send({type:v.ClearSearch}),350));break}}),ke=$(c=>{switch(c.key){case w.Space:c.preventDefault();break}}),Te=u.useMemo(()=>({open:S===M.Open}),[S]),Fe=Ee(b?p():{},{"aria-activedescendant":k(a,a.selectors.activeDescendantId),"aria-labelledby":k(a,c=>{var j;return(j=c.buttonElement)==null?void 0:j.id}),id:l,onKeyDown:De,onKeyUp:ke,role:"menu",tabIndex:S===M.Open?0:void 0,ref:I,style:{...d.style,...g,"--button-width":nt(m,!0).width},...tt(G)}),Re=O();return T.createElement(rt,{enabled:r?e.static||A:!1,ownerDocument:D},Re({ourProps:Fe,theirProps:d,slot:Te,defaultTag:jt,features:Ut,visible:Q,name:"Menu.Items"}))}let Ht=u.Fragment;function Vt(e,n){let t=u.useId(),{id:l=`headlessui-menu-item-${t}`,disabled:s=!1,...r}=e,o=ee("Menu.Item"),i=k(o,h=>o.selectors.isActive(h,l)),d=u.useRef(null),b=L(n,d),a=k(o,h=>o.selectors.shouldScrollIntoView(h,l));F(()=>{if(a)return he().requestAnimationFrame(()=>{var h,Q;(Q=(h=d.current)==null?void 0:h.scrollIntoView)==null||Q.call(h,{block:"nearest"})})},[a,d]);let y=Rt(d),g=u.useRef({disabled:s,domRef:d,get textValue(){return y()}});F(()=>{g.current.disabled=s},[g,s]),F(()=>(o.actions.registerItem(l,g),()=>o.actions.unregisterItem(l)),[g,l]);let p=$(()=>{o.send({type:v.CloseMenu})}),E=$(h=>{if(s)return h.preventDefault();o.send({type:v.CloseMenu}),$e(o.state.buttonElement)}),C=$(()=>{if(s)return o.send({type:v.GoToItem,focus:x.Nothing});o.send({type:v.GoToItem,focus:x.Specific,id:l})}),I=Pt(),S=$(h=>{I.update(h),!s&&(i||o.send({type:v.GoToItem,focus:x.Specific,id:l,trigger:Y.Pointer}))}),m=$(h=>{I.wasMoved(h)&&(s||i||o.send({type:v.GoToItem,focus:x.Specific,id:l,trigger:Y.Pointer}))}),D=$(h=>{I.wasMoved(h)&&(s||i&&o.send({type:v.GoToItem,focus:x.Nothing}))}),[P,_]=Ce(),[A,G]=bt(),N=u.useMemo(()=>({active:i,focus:i,disabled:s,close:p}),[i,s,p]),B={id:l,ref:b,role:"menuitem",tabIndex:s===!0?void 0:-1,"aria-disabled":s===!0?!0:void 0,"aria-labelledby":P,"aria-describedby":A,disabled:void 0,onClick:E,onFocus:C,onPointerEnter:S,onMouseEnter:S,onPointerMove:m,onMouseMove:m,onPointerLeave:D,onMouseLeave:D},f=O();return T.createElement(_,null,T.createElement(G,null,f({ourProps:B,theirProps:r,slot:N,defaultTag:Ht,name:"Menu.Item"})))}let Kt="div";function Yt(e,n){let[t,l]=Ce(),s=e,r={ref:n,"aria-labelledby":t,role:"group"},o=O();return T.createElement(l,null,o({ourProps:r,theirProps:s,slot:{},defaultTag:Kt,name:"Menu.Section"}))}let qt="header";function Wt(e,n){let t=u.useId(),{id:l=`headlessui-menu-heading-${t}`,...s}=e,r=Z();F(()=>r.register(l),[l,r.register]);let o={id:l,ref:n,role:"presentation",...r.props};return O()({ourProps:o,theirProps:s,slot:{},defaultTag:qt,name:"Menu.Heading"})}let zt="div";function Xt(e,n){let t=e,l={ref:n,role:"separator"};return O()({ourProps:l,theirProps:t,slot:{},defaultTag:zt,name:"Menu.Separator"})}let Zt=R(Lt),Jt=R(Bt),en=R(Qt),tn=R(Vt),nn=R(Yt),rn=R(Wt),ln=R(Xt),on=Object.assign(Zt,{Button:Jt,Items:en,Item:tn,Section:nn,Heading:rn,Separator:ln});export{on as l};
