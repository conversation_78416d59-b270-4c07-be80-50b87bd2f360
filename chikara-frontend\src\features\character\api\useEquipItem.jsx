import { useMutation, useQueryClient } from "@tanstack/react-query";
import { toast } from "react-hot-toast";
import { api } from "@/helpers/api";

const useEquipItem = () => {
    const queryClient = useQueryClient();

    const mutation = useMutation(
        api.user.equip.mutationOptions({
            onMutate: async (input) => {
                // Extract userItem from the context we'll pass
                const userItem = input._userItem;

                await queryClient.cancelQueries({
                    queryKey: api.user.equippedItems.key(),
                });

                const previousEquippedItems = queryClient.getQueryData(api.user.equippedItems.key());

                queryClient.setQueryData(api.user.equippedItems.key(), (old) => {
                    const optimisticUpdate = { ...old };
                    return { ...optimisticUpdate, [userItem.itemType]: userItem };
                });

                return { previousEquippedItems };
            },
            onError: (err, variables, context) => {
                queryClient.setQueryData(api.user.equippedItems.key(), context.previousEquippedItems);
                toast.error(err.message || "An error occurred");
            },
            onSettled: async () => {
                await queryClient.invalidateQueries({
                    queryKey: api.user.equippedItems.key(),
                });
            },
            onSuccess: async () => {
                await queryClient.invalidateQueries({
                    queryKey: api.user.equippedItems.key(),
                });
                toast.success("Item equipped!");
            },
        })
    );

    // Wrapper function to handle the ORPC input format and user state validation
    const equipItem = (variables) => {
        const { currentUser, userItem } = variables;

        // Client-side validation (same as before)
        if (currentUser?.hospitalisedUntil > 0) {
            toast.error("Can't equip items while hospitalised!");
            return Promise.reject(new Error("Can't equip items while hospitalised!"));
        }
        if (currentUser?.jailedUntil > 0) {
            toast.error("Can't equip items while jailed!");
            return Promise.reject(new Error("Can't equip items while jailed!"));
        }

        // Call the ORPC mutation with the correct input format
        // Pass userItem as a private property for optimistic updates
        return mutation.mutate({
            userItemId: userItem.id,
            _userItem: userItem, // Pass userItem for optimistic updates
        });
    };

    return {
        equipItem: { ...mutation, mutate: equipItem },
    };
};

export default useEquipItem;
