import{r as u,j as e,ab as L,ac as q,e as M,g as O,c as y,l as p,ad as z,ae as A,af as F,v as E,o as k,h as f,N as I,ag as R,a6 as V,C as _,q as P,B as G,b as v,t as H,M as w,ah as $,ai as D}from"./index-DOR_A_Wy.js";import{u as K}from"./useGetInventory-DVvdQ2TV.js";import{T as Q}from"./TraderRep-QjcrwDLv.js";import{A as S}from"./arrow-left-CbJy6pIq.js";const U=({shopData:a,currentUser:r})=>{const[i,l]=u.useState({id:0,cashValue:0,item:{image:""},name:""}),[o,c]=u.useState(!1),s=a?.shop_listing?.sort((n,x)=>n.cost-x.cost);return e.jsxs(e.Fragment,{children:[a?.shop_listing?.length===0&&e.jsx("div",{className:"flex",children:e.jsx("p",{className:"mx-auto mt-12 text-2xl dark:text-gray-200",children:"No items available!"})}),e.jsx("div",{className:"mx-3 mt-4 mb-6 grid grid-cols-3 gap-x-3 gap-y-6 sm:grid-cols-2 sm:gap-x-6 md:mt-6 lg:grid-cols-5 xl:gap-x-3",children:s?.map(n=>e.jsx(u.Fragment,{children:e.jsx(L,{product:n,setOpenModal:c,setItemToBuy:l})},n.id))}),e.jsx(q,{openModal:o,setOpenModal:c,itemToBuy:i,currentUser:r})]})};function Y({openModal:a,setOpenModal:r,itemToSell:i}){const[l,o]=u.useState(1),c=M(),s={...i?.item};s.rarity==="novice"&&(s.colour="text-stroke-s-sm"),s.rarity==="standard"&&(s.colour="text-green-600"),s.rarity==="enhanced"&&(s.colour="text-blue-600"),s.rarity==="specialist"&&(s.colour="text-indigo-600"),s.rarity==="military"&&(s.colour="text-red-600"),s.rarity==="legendary"&&(s.colour="text-yellow-600");const n=O(y.shops.sellItem.mutationOptions({onSuccess:()=>{r(!1),p.success(`${Number.parseInt(l)}x ${s.name} sold for ¥${s.cashValue*Number.parseInt(l)}`),c.invalidateQueries({queryKey:y.user.inventory.key()}),c.invalidateQueries({queryKey:y.user.currentUserInfo.key()})},onError:d=>{r(!1),d.message.includes("equipped")?p.error("Cannot sell equipped items. Please unequip the item first."):d.message.includes("insufficient")?p.error("You don't have enough of this item to sell."):d.message.includes("not found")?p.error("Item not found in your inventory."):p.error(d.message||"Failed to sell item. Please try again.")}})),x=()=>{n.mutate({userItemId:Number.parseInt(i.id),amount:Number.parseInt(l)}),setTimeout(()=>o(1),500)},m=()=>{r(!1),setTimeout(()=>o(1),500)};return e.jsx(z,{showClose:!0,open:a,title:"SELL ITEM",iconBackground:"shadow-lg",Icon:()=>e.jsx("img",{src:"https://cloudflare-image.jamessut.workers.dev/ui-images/4AAKgyV.png",alt:"",className:"mt-0.5 h-11 w-auto"}),onOpenChange:m,children:e.jsxs("div",{className:"mt-3 text-center",children:[e.jsxs("div",{className:"mb-1 flex flex-col",children:[e.jsxs("div",{className:"mb-3 flex flex-col",children:[e.jsx(A,{item:i.item,height:"w-1/4 mx-auto"}),e.jsx("p",{className:"text-gray-200",children:i.item.name})]}),e.jsx("label",{className:"my-auto mr-3 block text-center font-medium text-gray-700 text-xs uppercase tracking-wide dark:text-gray-300",htmlFor:"quantity",children:"Amount"}),e.jsxs("div",{className:"mx-auto my-2 flex w-2/5 rounded-md shadow-xs md:mt-1",children:[e.jsx("input",{type:"text",id:"quantity",name:"quantity",className:"block w-full min-w-0 flex-1 rounded-l-md border-gray-300 px-3 py-2 text-center text-lg focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm md:text-xl dark:border-gray-500 dark:bg-gray-900 dark:text-white",placeholder:"1",min:"1",step:"1",max:i.count,value:l,onChange:d=>{o(d.target.value)}}),e.jsxs("span",{className:"inline-flex items-center rounded-r-md border border-gray-300 border-l-0 bg-gray-50 px-3 text-center text-gray-500 text-lg sm:text-sm md:text-xl dark:border-gray-500 dark:bg-gray-950 dark:text-white",children:["/ ",i.count]})]})]}),e.jsxs("p",{className:"mt-2 mb-1 text-base text-gray-500 md:text-lg dark:text-gray-300",children:["Are you sure you want to sell"," ",e.jsxs("span",{className:s.colour,children:[l,"x ",s.name]})," ","for ",e.jsxs("span",{className:" text-green-600",children:["¥",s.cashValue*l]}),"?"]}),e.jsx("button",{type:"button",className:"darkBlueButtonBGSVG mx-auto flex h-18 w-3/4 items-center justify-center rounded-xl font-lili text-[1.35rem] text-stroke-s-sm uppercase shadow-xs md:mt-3 dark:text-slate-200",onClick:x,children:"Sell"})]})})}function W({item:a,setOpenModal:r,setItemToSell:i}){return a?e.jsxs("div",{className:"group cursor-pointer divide-gray-200 rounded-lg shadow-sm",onClick:()=>{i(a),r(!0)},children:[e.jsx(F,{item:a?.item,type:"sell",count:a?.count}),e.jsx("div",{className:"-mt-1.5 flex flex-col",children:e.jsxs("div",{className:"relative mx-auto flex w-[90%] items-center justify-center rounded-b-md border border-transparent bg-gray-100 pt-3 pb-2 font-medium text-base text-gray-900 ring-2 ring-black group-hover:bg-gray-800 md:text-base dark:border dark:border-gray-700 dark:bg-gray-900 dark:text-gray-300 dark:group-hover:border-gray-400",children:[e.jsx("span",{className:"mr-0.5 text-green-500",children:"+"}),"¥",a?.item?.cashValue]})})]},a.id):null}const J=({currentUser:a})=>{const[r,i]=u.useState({id:0,cashValue:0,item:{image:""},name:""}),[l,o]=u.useState(!1),{data:c,isLoading:s}=K(),x=c?.sort((m,d)=>m?.item?.cashValue-d?.item?.cashValue)?.filter(m=>m?.item?.itemType!=="quest");return s?"Loading..":e.jsxs(e.Fragment,{children:[c?.length===0&&e.jsx("div",{className:"flex",children:e.jsx("p",{className:"mx-auto mt-12 text-2xl dark:text-gray-200",children:"You have no items to sell!"})}),e.jsx("div",{className:"mx-3 mt-4 mb-6 grid grid-cols-3 gap-x-3 gap-y-6 sm:grid-cols-2 sm:gap-x-6 md:mt-6 lg:grid-cols-5 xl:gap-x-3",children:x.map(m=>e.jsx(W,{item:m,setOpenModal:o,setItemToSell:i}))}),e.jsx(Y,{openModal:l,setOpenModal:o,itemToSell:r})]})},X=a=>{const r="https://cloudflare-image.jamessut.workers.dev/static/characters";switch(I(a)){case"Nagao":return r+"/Nagao/happyopenSmall.webp";case"Goda":return r+"/Goda/happyopenSmall.webp";case"Mihara":return r+"/Mihara/happySmall.webp";case"Shoko":return r+"/Shoko/happyopenSmall.webp";case"Otake":return r+"/Otake/neutralSmall.webp";case"Honda":return r+"/Honda/neutralSmall.webp";default:return""}};function Z({singleShop:a,setSellOrBuyTab:r,sellOrBuyTab:i,cash:l}){E();const o=s=>i===s,c=[{name:"Buy",current:o("Buy")},{name:"Sell",current:o("Sell")}];return e.jsxs("div",{className:"relative mb-0 flex flex-col bg-gray-200 md:mt-14 md:rounded-l-md dark:border-gray-600 dark:border-b dark:border-l dark:bg-gray-800",children:[e.jsxs("div",{className:"hidden sm:block",children:[e.jsx(k,{to:-1,children:e.jsxs("button",{type:"button",className:"-top-12 -left-8 absolute z-20 mx-3 mt-3 hidden items-center rounded-md border border-transparent bg-blue-600 px-6 py-1.5 text-md text-white shadow-xs hover:bg-blue-700 focus:outline-hidden focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 md:mx-8 md:mt-0 md:inline-flex dark:text-stroke-sm",children:[e.jsx(S,{className:"mr-1 size-3.5"}),"Back"]})}),e.jsx("nav",{className:"relative z-0 flex divide-x divide-gray-200 overflow-hidden rounded-tl-lg border-slate-300 border-t shadow-sm dark:divide-gray-600 dark:border-gray-600 dark:border-l-0","aria-label":"Tabs",children:c.map((s,n)=>e.jsxs("button",{"data-testid":s.name==="Buy"?"buy-item-button":"sell-item-button","aria-current":s.current?"page":void 0,className:f(s.current?"text-gray-900":"text-gray-500",n===0?"rounded-tl-lg":"",(n===c.length-1,""),"group relative min-w-0 flex-1 overflow-hidden bg-white px-4 py-3 text-center font-medium text-lg focus:z-10 dark:bg-gray-900 dark:text-white"),onClick:()=>{r(s.name)},children:[e.jsx("span",{children:s.name}),e.jsx("span",{"aria-hidden":"true",className:f(s.current?"bg-indigo-500":"bg-transparent","absolute inset-x-0 bottom-0 h-[0.15rem]")})]},s.name))})]}),e.jsx("div",{className:"mb-1 flex bg-gray-400 px-3 py-2 text-shadow shadow-sm dark:bg-slate-700",children:e.jsxs("h2",{className:"mx-auto text-2xl text-white dark:text-slate-200 dark:text-stroke-s-md",children:[a.name,"'s ",I(a.shopType).replace("Furniture","Sunday")," Shop"]})}),e.jsx("div",{className:"mx-auto my-2 flex flex-row rounded-xl bg-gray-300 px-2 py-1 ring-2 ring-gray-500 dark:border dark:border-black dark:bg-gray-600",children:e.jsx(Q,{heartWidth:"w-6",shopId:a.id})}),e.jsx("img",{className:"mx-auto mb-3 w-32 md:mb-0 md:w-1/2",src:R(X(a.name)),alt:""}),e.jsxs("div",{className:"absolute bottom-2 left-2 flex flex-row text-center text-gray-200 md:hidden",children:[e.jsx("img",{className:"mr-1 h-6 w-auto",src:V,alt:"yen"}),e.jsx("span",{className:f("my-auto",l.length>6?"text-sm":"text-lg"),children:l})]}),e.jsx(k,{to:-1,children:e.jsxs("button",{type:"button",className:"-left-1 absolute top-11 mx-3 mt-3 inline-flex items-center rounded-md border border-transparent bg-blue-600 px-6 py-1.5 text-md text-white shadow-xs hover:bg-blue-700 focus:outline-hidden focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 md:mx-8 md:mt-0 md:hidden dark:text-stroke-sm",children:[e.jsx(S,{className:"mr-1 size-3.5"}),"Back"]})}),e.jsx(k,{to:"/tasks",children:e.jsx("div",{className:"absolute top-12 right-2 flex items-center justify-center text-center md:static",children:e.jsx(_,{height:"h-10! md:h-12!",className:"mt-2! mb-3! md:mt-12! text-base! font-medium! w-24 text-white md:w-48 dark:text-stroke-sm",type:"primary",children:"Tasks"})})})]})}function h(...a){return a.filter(Boolean).join(" ")}function le(){const{shopID:a}=P(),[r,i]=u.useState("Buy"),[l,o]=u.useState(0),{LIMITED_STOCK_WEEKLY_PERSONAL_LIMIT:c}=G(),{data:s}=v(y.shops.getTraderRep.queryOptions({input:{shopId:Number.parseInt(a)},enabled:!!a&&!isNaN(Number.parseInt(a))&&Number.parseInt(a)>0})),{data:n,isLoading:x}=v(y.shops.shopInfo.queryOptions({input:{shopId:Number.parseInt(a)},enabled:!!a&&!isNaN(Number.parseInt(a))&&Number.parseInt(a)>0})),{data:m}=H(),d=s?.reputationLevel||0,T=n?.shop_listing.filter(t=>t?.repRequired<=d),B=n?.shop_listing.filter(t=>t?.repRequired===l),b={...n,shop_listing:l===0?T:B},j=[{heart:!1,current:l===0,amount:0,disabled:!1},{heart:!0,current:l===1,amount:1,disabled:!d||d<1||n?.id===6},{heart:!0,current:l===2,amount:2,disabled:!d||d<2||n?.id===6},{heart:!0,current:l===3,amount:3,disabled:!d||d<3||n?.id===6},{heart:!0,current:l===4,amount:4,disabled:!0}],N=[{name:"Buy",current:r==="Buy"},{name:"Sell",current:r==="Sell"}];function C(){return!(b.id===6&&b.disabled)}return x?"Loading..":n?.id===6&&!C()&&m?.userType!=="admin"?e.jsx("p",{className:"mt-2 text-center text-lg",children:"Shop is closed until 12:00 Sunday"}):e.jsx("main",{className:"-mx-4 md:-mt-8 flex-1 pb-8",children:e.jsx("div",{className:"mt-8",children:e.jsx("div",{className:"mx-auto max-w-6xl px-4 sm:px-6 lg:px-8",children:e.jsxs("div",{className:"mt-2 grid grid-cols-1 py-1 sm:grid-cols-2 md:mt-4 md:py-0 lg:grid-cols-3",children:[e.jsx(Z,{singleShop:b,setSellOrBuyTab:i,sellOrBuyTab:r,cash:m?.cash.toString()}),e.jsx("div",{className:"col-span-2 flex flex-col",children:e.jsxs("div",{className:"h-full border bg-white md:rounded-t-lg dark:border-gray-600 dark:bg-gray-800",children:[e.jsx("div",{className:"block sm:hidden",children:e.jsx("nav",{className:"relative z-0 flex divide-x divide-gray-200 overflow-hidden shadow-sm dark:divide-gray-600","aria-label":"Tabs",children:N.map((t,g)=>e.jsxs("button",{"aria-current":t.current?"page":void 0,"data-testid":t.name==="Buy"?"buy-item-button-mobile":"sell-item-button-mobile",className:h(t.current?"text-gray-900":"text-gray-500","",(g===N.length-1,""),"group relative min-w-0 flex-1 overflow-hidden bg-white px-4 py-2.5 text-center font-medium text-xl focus:z-10 dark:bg-gray-900 dark:text-white"),onClick:()=>{i(t.name)},children:[e.jsx("span",{children:t.name}),e.jsx("span",{"aria-hidden":"true",className:h(t.current?"bg-indigo-400":"bg-gray-700","absolute inset-x-0 bottom-0 h-[0.15rem]")})]},t.name))})}),e.jsxs("div",{children:[e.jsx("div",{className:h("md:hidden",r==="Buy"?"block":"hidden"),children:e.jsx("nav",{className:"relative z-0 flex divide-x divide-gray-200 border-t shadow-sm dark:divide-gray-600 dark:border-gray-700","aria-label":"Tabs",children:j.map((t,g)=>e.jsxs("button",{"aria-current":t.current?"page":void 0,className:h("",t.disabled?"cursor-default bg-gray-100 text-gray-600 dark:bg-slate-700 dark:text-gray-500":"bg-white text-gray-900 hover:bg-gray-50 dark:bg-slate-800 dark:text-gray-200","group relative flex min-w-0 flex-1 overflow-hidden p-4 text-center font-medium text-sm focus:z-10"),onClick:()=>{t.disabled||o(t.amount)},children:[t.heart?e.jsxs("div",{className:"mx-auto flex flex-row gap-1",children:[e.jsx("span",{className:"text-lg",children:t.amount}),e.jsx(w,{className:`w-6 stroke-2 drop-shadow-md ${t.disabled?"stroke-gray-600 text-gray-600 opacity-50 dark:stroke-gray-800":"text-pink-600 dark:stroke-black"}`},t.id)]}):e.jsx("span",{className:"mx-auto text-lg",children:"All"}),e.jsx("span",{"aria-hidden":"true",className:h(t.current?"bg-indigo-500":"bg-gray-700","absolute inset-x-0 bottom-0 h-1")})]},t.amount))})}),e.jsx("div",{className:"hidden md:block",children:e.jsx("nav",{className:"relative z-0 flex divide-x divide-gray-200 shadow-sm dark:divide-gray-600","aria-label":"Tabs",children:j.map((t,g)=>e.jsxs("button",{"aria-current":t.current?"page":void 0,className:h((t.current,""),g===0?"rounded-tl-lg":"",g===j.length-1?"rounded-tr-lg":"",t.disabled?"cursor-default bg-gray-100 dark:bg-slate-700":"bg-white hover:bg-gray-50 dark:bg-slate-800","group relative flex min-w-0 flex-1 overflow-hidden p-4 text-center font-medium text-sm focus:z-10"),onClick:()=>{t.disabled||o(t.amount)},children:[t.heart?e.jsx("div",{className:"mx-auto flex flex-row",children:[...Array(t.amount)].map(ee=>e.jsx(w,{className:`w-6 stroke-2 drop-shadow-md ${t.disabled?"stroke-gray-800 text-gray-600 opacity-50":"stroke-black text-pink-600"}`},t.id))}):e.jsx("span",{className:"mx-auto text-gray-200 text-lg text-stroke-sm",children:"All"}),e.jsx("span",{"aria-hidden":"true",className:h(t.current?"bg-indigo-500":"bg-gray-700","absolute inset-x-0 bottom-0 h-1")})]},t.amount))})})]}),e.jsx("div",{className:"flex flex-col pt-1",children:n?.id===6&&e.jsxs(e.Fragment,{children:[" ",e.jsxs("div",{className:"flex flex-col",children:[e.jsx("p",{className:"mx-auto text-orange-500",children:"This shop contains limited stock."}),e.jsxs("p",{className:"mx-auto text-custom-yellow",children:["Personal buy limit:"," ",e.jsx("span",{className:f(m?.weeklyBuyLimitRemaining>0?"text-custom-yellow":"text-red-500"),children:m?.weeklyBuyLimitRemaining}),"/",c]})]}),e.jsxs("div",{className:"-mb-4 mt-2 mr-3 ml-auto flex flex-row gap-1 text-base text-blue-500 md:mt-0",children:[e.jsx("p",{children:"Shop closes in:"}),e.jsx($,{showHours:!0,targetDate:D(),showSeconds:!1})]})]})}),r==="Buy"?e.jsx(U,{shopData:b,currentUser:m}):e.jsx(J,{currentUser:m})]})})]})})})})}export{le as default};
