import{F as j,b as h,c as n,e as f,g as p,an as c,t as N,B as v,j as e,az as u,M as g,cR as w,aF as D,ah as S,h as d,O as b,r as M,bY as k,ax as I,D as O,o as T,i as A}from"./index-DOR_A_Wy.js";import{T as L}from"./trending-up-B_UVligf.js";/**
 * @license lucide-react v0.525.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const R=[["rect",{x:"3",y:"8",width:"18",height:"4",rx:"1",key:"bkv52"}],["path",{d:"M12 8v13",key:"1c76mn"}],["path",{d:"M19 12v7a2 2 0 0 1-2 2H7a2 2 0 0 1-2-2v-7",key:"6wjy6b"}],["path",{d:"M7.5 8a2.5 2.5 0 0 1 0-5A4.8 8 0 0 1 12 8a4.8 8 0 0 1 4.5-5 2.5 2.5 0 0 1 0 5",key:"1ihvrl"}]],C=j("gift",R),E=(s={})=>h(n.shrine.dailyGoal.queryOptions({staleTime:6e4,...s})),G=(s={})=>h(n.shrine.getDonations.queryOptions({staleTime:3e4,...s})),F=s=>{const t=f();return p(n.shrine.donate.mutationOptions({onSuccess:()=>{t.invalidateQueries({queryKey:n.shrine.dailyGoal.key()}),t.invalidateQueries({queryKey:n.shrine.getDonations.key()}),t.invalidateQueries({queryKey:n.shrine.activeShrineBuffer.key()}),t.invalidateQueries({queryKey:n.user.currentUserInfo.key()}),c.success("Donation Successful!"),s&&s()},onError:a=>{console.error("Shrine donation error:",a),c.error(a.message||"Donation failed. Please try again.")}}))};function Q(){const{isLoading:s,data:t}=E(),{data:a}=N(),{data:i}=G(),r=v();return s?e.jsx("div",{className:"min-h-screen bg-gray-950 flex items-center justify-center",children:e.jsxs("div",{className:"text-center text-gray-400",children:[e.jsx(u,{className:"w-12 h-12 mx-auto mb-4 animate-pulse text-violet-400"}),e.jsx("p",{className:"text-lg",children:"Loading Shrine..."})]})}):r?.ROGUELIKE_DISABLED&&a?.userType!=="admin"?e.jsx("div",{className:"min-h-screen bg-gray-950 flex items-center justify-center",children:e.jsxs("div",{className:"bg-gray-900/50 border border-gray-800 rounded-xl p-8 max-w-md mx-auto text-center",children:[e.jsx(g,{className:"w-16 h-16 mx-auto mb-4 text-gray-600"}),e.jsx("h2",{className:"text-2xl font-bold text-white mb-2",children:"The Shrine is Currently Disabled"}),e.jsx("p",{className:"text-gray-400",children:"Please return later to make your offerings."})]})}):e.jsx("div",{className:"min-h-screen bg-gray-950 text-white",children:e.jsx("div",{className:"max-w-7xl mx-auto px-4 py-6",children:e.jsxs("div",{className:"grid grid-cols-1 xl:grid-cols-3 gap-6",children:[e.jsxs("div",{className:"xl:col-span-2 space-y-6",children:[e.jsx(q,{shrineGoal:t}),e.jsx(U,{shrineConfig:r,currentUser:a})]}),e.jsx("div",{className:"xl:col-span-1",children:e.jsx(_,{recentDonations:i})})]})})})}const q=({shrineGoal:s})=>{const t=s?.goalReached||s?.donationAmount>=s?.donationGoal,a=w(),i=Object.values(s?.buffRewards||{}),r=s?.donationGoal?Math.min((s.donationAmount||0)/s.donationGoal*100,100):0;return e.jsxs("section",{className:"bg-gray-900/50 border border-gray-800 rounded-xl p-4 md:p-6",children:[e.jsxs("div",{className:"flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4 mb-4",children:[e.jsxs("div",{className:"flex items-center gap-3",children:[e.jsx("div",{className:"p-2 bg-violet-600 rounded-lg",children:e.jsx(L,{className:"w-5 h-5 text-white"})}),e.jsx("h2",{className:"text-xl font-bold text-white",children:"Daily Goal"})]}),e.jsxs("div",{className:"bg-gray-900 rounded-lg px-3 py-2 border border-gray-800",children:[e.jsxs("div",{className:"flex items-center gap-2 text-gray-400 text-xs",children:[e.jsx(D,{className:"w-3 h-3"}),e.jsx("span",{children:"Resets in"})]}),e.jsx("div",{className:"text-violet-400 font-bold text-sm",children:e.jsx(S,{showHours:!0,targetDate:a,showSeconds:!1})})]})]}),e.jsxs("div",{className:"text-center mb-4",children:[e.jsx("div",{className:d("inline-flex items-center gap-2 px-3 py-1 rounded-full text-xs font-semibold mb-3",t?"bg-green-900/50 text-green-400 border border-green-800":"bg-gray-800 text-gray-400 border border-gray-700"),children:t?e.jsxs(e.Fragment,{children:[e.jsx(b,{className:"w-3 h-3"}),"Daily Goal Complete!"]}):e.jsxs(e.Fragment,{children:[e.jsx(u,{className:"w-3 h-3"}),"Goal In Progress"]})}),e.jsxs("div",{className:"space-y-1",children:[e.jsxs("p",{className:"text-2xl font-bold text-white",children:["¥",s?.donationAmount?.toLocaleString()||0]}),e.jsxs("p",{className:"text-gray-500 text-sm",children:["of ¥",s?.donationGoal?.toLocaleString()||0," goal"]})]})]}),e.jsxs("div",{className:"relative mb-4",children:[e.jsxs("div",{className:"absolute -top-2 right-0 bg-gray-800 text-gray-300 text-xs px-2 py-0.5 rounded-full",children:[Math.round(r),"%"]}),e.jsx("div",{className:"h-2 bg-gray-800 rounded-full overflow-hidden",children:e.jsx("div",{className:"h-full bg-gradient-to-r from-violet-600 to-violet-500 transition-all duration-500 ease-out",style:{width:`${r}%`}})})]}),e.jsxs("div",{children:[e.jsxs("h3",{className:"text-sm font-semibold text-gray-300 mb-3 flex items-center gap-2",children:[e.jsx(C,{className:d("w-4 h-4",t?"text-green-400":"text-gray-600")}),"Today's Blessings"]}),e.jsx("div",{className:"grid grid-cols-1 sm:grid-cols-2 gap-2",children:i.map((x,l)=>e.jsxs("div",{className:d("flex items-center gap-2 p-2 rounded-lg text-xs transition-all",t?"bg-green-900/20 border border-green-800/50 text-green-400":"bg-gray-800/50 border border-gray-700/50 text-gray-500"),children:[e.jsx("div",{className:d("w-1.5 h-1.5 rounded-full",t?"bg-green-400":"bg-gray-600")}),e.jsx("span",{className:"font-medium text-blue-500",children:x.description})]},l))})]})]})},U=({shrineConfig:s,currentUser:t})=>{const[a,i]=M.useState(""),r=s?.SHRINE_MINIMUM_DONATION||100,{mutate:x,isPending:l}=F(()=>{i("")}),y=m=>{m.preventDefault();const o=parseInt(a,10);if(isNaN(o)||o<=0){c.error("Please enter a valid donation amount.");return}if(o>t?.cash){c.error("You don't have enough cash to donate!");return}if(o<r){c.error(`Minimum donation is ¥${r}.`);return}x({amount:o})};return e.jsxs("section",{className:"bg-gray-900/50 border border-gray-800 rounded-xl p-4 md:p-6",children:[e.jsxs("div",{className:"flex items-center gap-3 mb-4",children:[e.jsx("div",{className:"p-2 bg-amber-600 rounded-lg",children:e.jsx(k,{className:"w-5 h-5 text-white"})}),e.jsx("h2",{className:"text-xl font-bold text-white",children:"Make an Offering"})]}),e.jsxs("div",{className:"bg-gray-800/50 rounded-lg p-3 mb-4 border border-gray-700",children:[e.jsxs("div",{className:"flex items-center justify-between mb-1",children:[e.jsx("span",{className:"text-gray-400 text-sm",children:"Your Balance"}),e.jsxs("span",{className:"text-lg font-bold text-white",children:["¥",t?.cash?.toLocaleString()||0]})]}),e.jsxs("div",{className:"text-xs text-gray-500",children:["Minimum offering: ¥",r.toLocaleString()]})]}),e.jsxs("form",{className:"space-y-3",onSubmit:y,children:[e.jsxs("div",{className:"relative",children:[e.jsx("div",{className:"absolute inset-y-0 left-0 flex items-center pl-3 pointer-events-none",children:e.jsx("span",{className:"text-gray-500 font-semibold",children:"¥"})}),e.jsx("input",{type:"number",name:"amount",id:"amount",min:r,className:"block w-full rounded-lg border border-gray-700 bg-gray-800 pl-10 pr-4 py-3 text-white placeholder-gray-500 focus:border-violet-500 focus:ring-1 focus:ring-violet-500/20 focus:outline-none transition-all",placeholder:`Enter amount (min ${r})`,value:a,disabled:l,onChange:m=>i(m.target.value)})]}),e.jsx("button",{type:"submit",disabled:l,className:d("w-full h-10 px-4 rounded-lg font-medium transition-all duration-200","flex items-center justify-center gap-2",l?"bg-gray-700 text-gray-400 cursor-not-allowed":"bg-violet-600 hover:bg-violet-700 text-white"),children:l?e.jsxs(e.Fragment,{children:[e.jsx("div",{className:"w-4 h-4 border-2 border-gray-500 border-t-white rounded-full animate-spin"}),e.jsx("span",{children:"Offering..."})]}):e.jsxs(e.Fragment,{children:[e.jsx(g,{className:"w-4 h-4"}),e.jsx("span",{children:"Make Offering"})]})})]})]})},_=({recentDonations:s})=>e.jsxs("aside",{className:"bg-gray-900/50 border border-gray-800 rounded-xl p-4 md:p-6 h-full",children:[e.jsxs("div",{className:"flex items-center gap-3 mb-4",children:[e.jsx("div",{className:"p-2 bg-violet-600 rounded-lg",children:e.jsx(I,{className:"w-5 h-5 text-white"})}),e.jsx("h2",{className:"text-xl font-bold text-white",children:"Recent Offerings"})]}),e.jsxs("div",{className:"space-y-2",children:[s?.map((t,a)=>e.jsx("div",{className:"bg-gray-800/50 rounded-lg p-3 border border-gray-700 hover:bg-gray-800/70 transition-all duration-200",children:e.jsxs("div",{className:"flex items-center gap-3",children:[e.jsxs("div",{className:"relative",children:[e.jsx(O,{className:"h-10 w-10 rounded-full border border-gray-700",src:t?.user}),a===0&&e.jsx("div",{className:"absolute -top-1 -right-1 w-4 h-4 bg-amber-600 rounded-full flex items-center justify-center",children:e.jsx(b,{className:"w-2.5 h-2.5 text-white"})})]}),e.jsxs("div",{className:"flex-1 min-w-0",children:[e.jsx(T,{to:`/profile/${t?.userId}`,className:"font-medium text-white hover:text-violet-400 transition-colors block truncate text-sm",children:t?.user.username}),e.jsxs("div",{className:"flex items-center gap-2 text-xs",children:[e.jsxs("span",{className:"text-amber-400 font-bold",children:["¥",t?.amount.toLocaleString()]}),e.jsx("span",{className:"text-gray-600",children:"•"}),e.jsxs("span",{className:"text-gray-500",children:[A(t?.createdAt)," ago"]})]})]})]})},t.id)),(!s||s.length===0)&&e.jsxs("div",{className:"text-center py-8 text-gray-500",children:[e.jsx(g,{className:"w-10 h-10 mx-auto mb-3 text-gray-700"}),e.jsx("p",{className:"text-sm",children:"No recent offerings yet"}),e.jsx("p",{className:"text-xs mt-1",children:"Be the first to contribute!"})]})]})]});export{Q as default};
