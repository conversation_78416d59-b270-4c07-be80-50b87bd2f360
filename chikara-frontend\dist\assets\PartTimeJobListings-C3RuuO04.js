import{F as y,e as k,a as x,g as w,c as p,y as C,an as I,a7 as R,j as e,h as c,l as m,b as J,t as O,o as S}from"./index-DOR_A_Wy.js";import{g as E,B as L}from"./getJobImage-BaMDnpex.js";import{B as q}from"./banknote-CUuwbSST.js";import{C as z}from"./circle-check-big-DwPdKlj3.js";import{C as M}from"./circle-x-DoQtT0sc.js";import{M as T}from"./map-pin-baH9DEpl.js";import{A as B}from"./arrow-left-CbJy6pIq.js";/**
 * @license lucide-react v0.525.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const A=[["path",{d:"m10 17 5-5-5-5",key:"1bsop3"}],["path",{d:"M15 12H3",key:"6jk70r"}],["path",{d:"M15 3h4a2 2 0 0 1 2 2v14a2 2 0 0 1-2 2h-4",key:"u53s6r"}]],F=y("log-in",A),U=t=>{const o=k(),i=x();return w(p.jobs.applyForJob.mutationOptions({onSuccess:(a,u)=>{a.success&&(o.invalidateQueries({queryKey:C.JOBS.CURRENTJOBINFO}),I.success("Job application was successful!"),i("/job"),t?.onSuccess&&t.onSuccess())},onError:a=>{console.error("Job application error:",a),t?.onError&&t.onError(a)}}))};function P({job:t,currentJob:o,currentUser:i}){const a=R("investor"),u=s=>a?Math.round(s*(a.modifier||1)):s,g=x(),h={backgroundImage:t?.avatar?`url(${t.avatar})`:"none",backgroundRepeat:"no-repeat",backgroundPosition:"center",backgroundSize:"cover",backgroundColor:"black",opacity:"1",width:"100%",height:"100%",position:"absolute",top:"0",left:"0",borderRadius:"0.75rem"},f=U({onSuccess:()=>{m.success("You were hired!"),g("/job")},onError:()=>{m.error("Your stats don't meet the requirements for this job!")}}),j=s=>{s===o?m.error("You already work for this company!"):f.mutate({jobId:s})},b=Object.entries(t.baseRequirements).filter(([s,r])=>s!=="payment"&&r>0).sort((s,r)=>r[1]-s[1]).map(([s,r])=>({[s]:r})),v=s=>({strength:"STR",intelligence:"INT",dexterity:"DEX",defence:"DEF",endurance:"END",vitality:"VIT"})[s]||"",N=(s,r=!1)=>{const d=Object.keys(s)[0],l=Object.values(s)[0];return i[d]>=l?r?!0:"meets":r?!1:"fails"},n=t.id===o;return e.jsxs("div",{className:c("group relative overflow-hidden rounded-xl border transition-all duration-300",n?"border-emerald-600 shadow-emerald-900/20":"border-slate-700 hover:border-slate-600 hover:shadow-xl"),children:[e.jsx("div",{style:h}),e.jsx("div",{className:"absolute inset-0 bg-gradient-to-t from-black via-black/80 to-black/40"}),e.jsx("div",{className:"absolute inset-0 opacity-10",children:e.jsx("div",{className:"h-full w-full",style:{backgroundImage:"repeating-linear-gradient(45deg, transparent, transparent 35px, rgba(255,255,255,.1) 35px, rgba(255,255,255,.1) 70px)"}})}),n&&e.jsx("div",{className:"absolute inset-0 bg-gradient-to-t from-emerald-900/30 to-transparent"}),e.jsxs("div",{className:"relative z-10 p-6",children:[e.jsxs("div",{className:"mb-4 flex items-start justify-between",children:[e.jsx("div",{className:"rounded-lg bg-black/40 p-2 backdrop-blur-sm",children:e.jsx("img",{className:"size-16 rounded-md shadow-lg transition-transform group-hover:scale-105",src:E(t.name),alt:t.name})}),n&&e.jsx("span",{className:"rounded-full bg-emerald-900/80 px-3 py-1 text-xs font-semibold text-emerald-300 backdrop-blur-sm border border-emerald-600/50",children:"CURRENT JOB"})]}),e.jsxs("div",{className:"mb-4",children:[e.jsx("h3",{className:"mb-2 text-xl font-bold text-white drop-shadow-lg",children:t.name}),e.jsxs("div",{className:"flex items-center gap-2 rounded-lg bg-black/40 px-3 py-2 backdrop-blur-sm",children:[e.jsx(q,{className:"size-4 text-custom-yellow"}),e.jsxs("span",{className:"font-semibold text-custom-yellow",children:["¥",u(t.baseRequirements.payment),"/day"]})]})]}),e.jsxs("div",{className:"space-y-3",children:[e.jsx("p",{className:"text-xs font-semibold uppercase tracking-wider text-gray-300 drop-shadow",children:"Requirements"}),e.jsx("div",{className:c("grid gap-2",b.length>2?"grid-cols-3":"grid-cols-2"),children:b.map(s=>{const r=Object.keys(s)[0],d=Object.values(s)[0],l=N(s,!0);return e.jsxs("div",{className:c("flex items-center justify-between rounded-lg px-3 py-2 text-sm font-medium transition-colors backdrop-blur-sm border",l?"bg-emerald-900/40 text-emerald-300 border-emerald-600/50":"bg-red-900/40 text-red-300 border-red-600/50"),children:[e.jsx("span",{children:v(r)}),e.jsxs("div",{className:"flex items-center gap-1",children:[e.jsx("span",{children:d}),l?e.jsx(z,{className:"size-3"}):e.jsx(M,{className:"size-3"})]})]},r)})})]}),e.jsx("button",{disabled:n,className:c("mt-4 flex w-full items-center justify-center gap-2 rounded-lg py-3 font-semibold text-white transition-all backdrop-blur-sm border",n?"cursor-not-allowed bg-slate-800/50 opacity-50 border-slate-600/50":"bg-gradient-to-r from-indigo-600/80 to-indigo-700/80 hover:from-indigo-700/90 hover:to-indigo-800/90 hover:shadow-lg border-indigo-500/50 hover:border-indigo-400/50"),onClick:()=>j(t.id),children:n?e.jsxs(e.Fragment,{children:[e.jsx(T,{className:"size-4"}),e.jsx("span",{children:"Current Workplace"})]}):e.jsxs(e.Fragment,{children:[e.jsx(F,{className:"size-4"}),e.jsx("span",{children:"Apply Now"})]})})]})]})}const V=()=>J(p.jobs.jobList.queryOptions());function $(){const{data:t}=O(),{data:o,isLoading:i}=V();return i?e.jsx("div",{className:"flex h-screen items-center justify-center bg-gradient-to-b from-slate-900 to-black",children:e.jsx("div",{className:"text-lg text-gray-400",children:"Loading job listings..."})}):e.jsx("div",{className:"min-h-screen bg-gradient-to-b from-slate-900 to-black p-4 md:p-8",children:e.jsxs("div",{className:"mx-auto max-w-7xl",children:[e.jsx("div",{className:"mb-8",children:e.jsxs("div",{className:"flex items-center justify-between mb-6",children:[t?.jobId!==null&&e.jsx(S,{to:-1,children:e.jsxs("button",{type:"button",className:"inline-flex items-center gap-2 rounded-lg bg-slate-700 px-4 py-2.5 font-medium text-white transition-colors hover:bg-slate-600",children:[e.jsx(B,{className:"size-4"}),e.jsx("span",{className:"text-sm",children:"Back"})]})}),e.jsxs("div",{className:"ml-auto",children:[e.jsxs("h1",{className:"text-3xl font-bold text-white flex items-center gap-3",children:[e.jsx(L,{className:"size-8 text-indigo-400"}),"Job Listings"]}),e.jsx("p",{className:"mt-2 text-gray-400",children:"Choose your career path"})]})]})}),e.jsx("div",{className:"grid gap-6 sm:grid-cols-2 lg:grid-cols-3",children:o?.map(a=>e.jsx(P,{job:a,currentJob:t?.jobId||0,currentUser:t},a.id))})]})})}export{$ as default};
