import{F as h,r as v,j as e,h as m,X as q,aO as H,d3 as V,d4 as G,d5 as W,d6 as Y,l as M,N as K,e as D,g as O,c as x,an as _,bQ as F,Z as P,o as X,bL as Z,d7 as J,d8 as ee,d9 as se,da as te,J as C,v as ie,ae,t as ne,K as re,M as z,O as oe,G as le}from"./index-DOR_A_Wy.js";import{u as $}from"./useGetUserSkills-Dwy3R08u.js";import{t as B}from"./talentData-CZzZ7076.js";import{A as E}from"./arrow-left-CbJy6pIq.js";import{W as ce,P as L}from"./wallet-CG9ihtJa.js";import{T as de}from"./trending-up-B_UVligf.js";import{D as ue}from"./dumbbell-BsPOtb-G.js";import{B as me}from"./brain-B_UQSFJf.js";import{H as A}from"./hammer-BSeqG4Jn.js";/**
 * @license lucide-react v0.525.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const pe=[["path",{d:"M 22 14 L 22 10",key:"nqc4tb"}],["rect",{x:"2",y:"6",width:"16",height:"12",rx:"2",key:"13zb55"}]],xe=h("battery",pe);/**
 * @license lucide-react v0.525.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const ge=[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["circle",{cx:"12",cy:"12",r:"1",key:"41hilf"}]],he=h("circle-dot",ge);/**
 * @license lucide-react v0.525.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const fe=[["path",{d:"M12 20v2",key:"1lh1kg"}],["path",{d:"M12 2v2",key:"tus03m"}],["path",{d:"M17 20v2",key:"1rnc9c"}],["path",{d:"M17 2v2",key:"11trls"}],["path",{d:"M2 12h2",key:"1t8f8n"}],["path",{d:"M2 17h2",key:"7oei6x"}],["path",{d:"M2 7h2",key:"asdhe0"}],["path",{d:"M20 12h2",key:"1q8mjw"}],["path",{d:"M20 17h2",key:"1fpfkl"}],["path",{d:"M20 7h2",key:"1o8tra"}],["path",{d:"M7 20v2",key:"4gnj0m"}],["path",{d:"M7 2v2",key:"1i4yhu"}],["rect",{x:"4",y:"4",width:"16",height:"16",rx:"2",key:"1vbyd7"}],["rect",{x:"8",y:"8",width:"8",height:"8",rx:"1",key:"z9xiuo"}]],be=h("cpu",fe);/**
 * @license lucide-react v0.525.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const ye=[["path",{d:"M11.562 3.266a.5.5 0 0 1 .876 0L15.39 8.87a1 1 0 0 0 1.516.294L21.183 5.5a.5.5 0 0 1 .798.519l-2.834 10.246a1 1 0 0 1-.956.734H5.81a1 1 0 0 1-.957-.734L2.02 6.02a.5.5 0 0 1 .798-.519l4.276 3.664a1 1 0 0 0 1.516-.294z",key:"1vdc57"}],["path",{d:"M5 21h14",key:"11awu3"}]],je=h("crown",ye);/**
 * @license lucide-react v0.525.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const ve=[["path",{d:"M10 2v6.292a7 7 0 1 0 4 0V2",key:"1s42pc"}],["path",{d:"M5 15h14",key:"m0yey3"}],["path",{d:"M8.5 2h7",key:"csnxdl"}]],Ne=h("flask-round",ve);/**
 * @license lucide-react v0.525.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const we=[["path",{d:"M4 16v-2.38C4 11.5 2.97 10.5 3 8c.03-2.72 1.49-6 4.5-6C9.37 2 10 3.8 10 5.5c0 3.11-2 5.66-2 8.68V16a2 2 0 1 1-4 0Z",key:"1dudjm"}],["path",{d:"M20 20v-2.38c0-2.12 1.03-3.12 1-5.62-.03-2.72-1.49-6-4.5-6C14.63 6 14 7.8 14 9.5c0 3.11 2 5.66 2 8.68V20a2 2 0 1 0 4 0Z",key:"l2t8xc"}],["path",{d:"M16 17h4",key:"1dejxt"}],["path",{d:"M4 13h4",key:"1bwh8b"}]],R=h("footprints",we);/**
 * @license lucide-react v0.525.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const ke=[["path",{d:"M18 11V6a2 2 0 0 0-2-2a2 2 0 0 0-2 2",key:"1fvzgz"}],["path",{d:"M14 10V4a2 2 0 0 0-2-2a2 2 0 0 0-2 2v2",key:"1kc0my"}],["path",{d:"M10 10.5V6a2 2 0 0 0-2-2a2 2 0 0 0-2 2v8",key:"10h0bg"}],["path",{d:"M18 8a2 2 0 1 1 4 0v6a8 8 0 0 1-8 8h-2c-2.8 0-4.5-.86-5.99-2.34l-3.6-3.6a2 2 0 0 1 2.83-2.82L7 15",key:"1s1gnw"}]],qe=h("hand",ke);/**
 * @license lucide-react v0.525.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const _e=[["path",{d:"M20.38 3.46 16 2a4 4 0 0 1-8 0L3.62 3.46a2 2 0 0 0-1.34 2.23l.58 3.47a1 1 0 0 0 .99.84H6v10c0 1.1.9 2 2 2h8a2 2 0 0 0 2-2V10h2.15a1 1 0 0 0 .99-.84l.58-3.47a2 2 0 0 0-1.34-2.23z",key:"1wgbhj"}]],I=h("shirt",_e),Pe={fabrication:{description:"Creating/repairing basic weapons & heavy gear from raw/salvaged materials."},outfitting:{description:"Crafting/repairing protective wear, utility gear, bags from lighter materials."},chemistry:{description:"Mixing consumables, buffs, debuffs, poisons from botanical/chemical sources."},electronics:{description:"Crafting/installing advanced mods, gadgets, complex repairs involving circuitry/mechanics."}},N={fabrication:{skill:"fabrication",spentPoints:0,talents:[{id:"fab_0",name:"Fabrication Basics",description:"Your understanding of basic fabrication begins. Unlocks fundamental recipes for simple melee weapons, basic processed materials (e.g., Crude Ingots), and simple upgrade materials (e.g., Basic Weight).",icon:"🛠️",requiredPoints:0,requiredTalents:[],bonuses:["Unlock Tier 1 Fabrication recipes"],unlocked:!1,position:{x:50,y:5}},{id:"fab_1",name:"Material Efficiency",description:"You've learned to make better use of raw resources. Reduces the material cost for all Fabrication recipes by 5%.",icon:"♻️",requiredPoints:1,requiredTalents:["fab_0"],bonuses:["-5% material cost for Fabrication recipes"],unlocked:!1,position:{x:10,y:20}},{id:"fab_2",name:"Workshop Speed",description:"Practice makes perfect... and faster. Increases Fabrication crafting speed by 10%.",icon:"⏱️",requiredPoints:1,requiredTalents:["fab_0"],bonuses:["+10% Fabrication crafting speed"],unlocked:!1,position:{x:50,y:20}},{id:"fab_3",name:"Structural Insight",description:"You grasp the fundamentals of strong construction. Crafted weapons and shields gain +5% maximum durability.",icon:"🧱",requiredPoints:1,requiredTalents:["fab_0"],bonuses:["+5% max durability for crafted weapons/shields"],unlocked:!1,position:{x:80,y:20}},{id:"fab_4",name:"Advanced Material Efficiency",description:"Further refines your techniques to conserve resources. Reduces the material cost for all Fabrication recipes by an additional 10% (15% total).",icon:"♻️+",requiredPoints:5,requiredTalents:["fab_1"],bonuses:["-10% material cost for Fabrication recipes"],unlocked:!1,position:{x:10,y:35}},{id:"fab_5",name:"Resourceful Processing",description:"You can extract more usable material from raw resources. Increases the yield of processed materials (ingots, sheets, parts) by 10%.",icon:"🔩",requiredPoints:5,requiredTalents:["fab_1","fab_2"],bonuses:["+10% yield for processed materials"],unlocked:!1,position:{x:35,y:35}},{id:"fab_6",name:"Melee Weapon Focus",description:"Specialize in crafting melee weapons. Unlocks Tier 2 melee weapon recipes and grants crafted melee weapons +5% base damage.",icon:"⚔️",requiredPoints:5,requiredTalents:["fab_2","fab_3"],bonuses:["Unlock Tier 2 Melee recipes","+5% base damage (crafted melee)"],unlocked:!1,position:{x:65,y:35}},{id:"fab_7",name:"Ranged Weapon Focus",description:"Specialize in crafting ranged weapon bodies and mechanisms. Unlocks Tier 2 ranged weapon recipes and grants crafted ranged weapons +5% base accuracy.",icon:"🏹",requiredPoints:5,requiredTalents:["fab_2","fab_3"],bonuses:["Unlock Tier 2 Ranged recipes","+5% base accuracy (crafted ranged)"],unlocked:!1,position:{x:95,y:35}},{id:"fab_8",name:"Reinforced Construction",description:"Master techniques for creating exceptionally sturdy items. Crafted weapons and shields gain an additional +10% maximum durability (15% total).",icon:"🧱+",requiredPoints:5,requiredTalents:["fab_3"],bonuses:["+10% max durability for crafted weapons/shields"],unlocked:!1,position:{x:80,y:35}},{id:"fab_9",name:"Alloy Development",description:"Unlock recipes for creating basic metal alloys, stronger than standard processed metals.",icon:"🧪",requiredPoints:10,requiredTalents:["fab_5"],bonuses:["Unlock basic Alloy recipes"],unlocked:!1,position:{x:20,y:50}},{id:"fab_10",name:"Impact Dynamics",description:"Refine the balance and striking surfaces of melee weapons. Crafted melee weapons gain +5% critical hit chance.",icon:"💥",requiredPoints:10,requiredTalents:["fab_6"],bonuses:["+5% critical hit chance (crafted melee)"],unlocked:!1,position:{x:55,y:50}},{id:"fab_11",name:"Sharpening Expertise",description:"Improve the quality and effectiveness of sharpening-based upgrade materials. Increases the effectiveness of crafted Sharpening Stones by 25%.",icon:"🔪",requiredPoints:10,requiredTalents:["fab_6","fab_8"],bonuses:["+25% effectiveness (crafted Sharpening Stones)"],unlocked:!1,position:{x:70,y:50}},{id:"fab_12",name:"Frame Optimization",description:"Improve the structural design of ranged weapon bodies. Crafted ranged weapons gain +5% range.",icon:"🔭",requiredPoints:10,requiredTalents:["fab_7"],bonuses:["+5% range (crafted ranged)"],unlocked:!1,position:{x:95,y:50}},{id:"fab_13",name:"Masterwork Chance",description:"Your skill occasionally produces exceptional results. 5% chance to craft an item with 10% higher base stats.",icon:"⭐",requiredPoints:15,requiredTalents:["fab_9","fab_11"],bonuses:["5% chance for +10% base stats on crafted items"],unlocked:!1,position:{x:30,y:65}},{id:"fab_14",name:"Brutal Force",description:"Design melee weapons intended for maximum impact. Crafted melee weapons gain +10% critical hit damage.",icon:"🔨",requiredPoints:15,requiredTalents:["fab_10"],bonuses:["+10% critical hit damage (crafted melee)"],unlocked:!1,position:{x:55,y:65}},{id:"fab_15",name:"Precision Engineering",description:"Fine-tune the mechanical components of ranged weapons. Crafted ranged weapons gain +5% armor penetration.",icon:"⚙️",requiredPoints:15,requiredTalents:["fab_12"],bonuses:["+5% armor penetration (crafted ranged)"],unlocked:!1,position:{x:95,y:65}},{id:"fab_16",name:"Strength Synergy",description:"Leverage your physical power in the crafting process. Gain a small bonus to crafting quality based on your STR attribute.",icon:"💪",requiredPoints:15,requiredTalents:["fab_8"],bonuses:["Minor crafting quality bonus scaling with STR"],unlocked:!1,position:{x:80,y:65}}]},outfitting:{skill:"outfitting",spentPoints:0,talents:[{id:"out_0",name:"Outfitting Basics",description:"You learn the fundamentals of stitching, cutting, and assembling basic gear.",icon:"👕",requiredPoints:0,requiredTalents:[],bonuses:["Unlock basic Outfitting recipes (simple clothes, basic padding)"],unlocked:!1,position:{x:50,y:5}},{id:"out_1",name:"Resourceful Stitching",description:"Learn to make the most of your fabrics and leathers.",icon:"🧵",requiredPoints:1,requiredTalents:["out_0"],bonuses:["5% reduced material cost for Head, Chest, Hands, Legs, Feet recipes"],unlocked:!1,position:{x:30,y:20}},{id:"out_2",name:"Dexterous Hands",description:"Your nimble fingers work faster and more precisely.",icon:"✂️",requiredPoints:1,requiredTalents:["out_0"],bonuses:["5% increased Outfitting crafting speed","Slight increase to minimum quality rolls"],unlocked:!1,position:{x:70,y:20}},{id:"out_3",name:"Efficient Tanning & Weaving",description:"Improve your yield when processing raw hides and fibers.",icon:"🌿",requiredPoints:2,requiredTalents:["out_1"],bonuses:["10% chance to produce extra Processed Materials (Leather, Cloth, Strapping)"],unlocked:!1,position:{x:15,y:40}},{id:"out_4",name:"Reinforced Seams",description:"Improve the structural integrity of your crafted gear.",icon:"✨",requiredPoints:2,requiredTalents:["out_1","out_2"],bonuses:["+10% durability to crafted Head, Chest, Hands, Legs, Feet items"],unlocked:!1,position:{x:50,y:40}},{id:"out_5",name:"Shield Crafting Primer",description:"Learn the basics of constructing protective shields.",icon:"🛡️",requiredPoints:2,requiredTalents:["out_2"],bonuses:["Unlock basic Shield recipes","5% reduced material cost for Shields"],unlocked:!1,position:{x:85,y:40}},{id:"out_6",name:"Advanced Material Processing",description:"Unlock techniques for creating higher quality processed materials.",icon:"🧪",requiredPoints:3,requiredTalents:["out_3"],bonuses:["Unlock recipes for Tier 2 Processed Materials (e.g., Hardened Leather, Ballistic Weave)"],unlocked:!1,position:{x:15,y:60}},{id:"out_7",name:"Padding Expertise",description:"Improve the effectiveness and unlock new types of protective padding.",icon:"🩹",requiredPoints:3,requiredTalents:["out_4"],bonuses:["+15% effectiveness to crafted Padding upgrades","Unlock recipes for improved Padding"],unlocked:!1,position:{x:40,y:60}},{id:"out_8",name:"Strap & Buckle Mastery",description:"Craft superior straps and kits for better gear utility.",icon:"🔗",requiredPoints:3,requiredTalents:["out_4"],bonuses:["+15% effectiveness to crafted Strap Kit upgrades","Unlock recipes for improved Strap Kits"],unlocked:!1,position:{x:60,y:60}},{id:"out_9",name:"Layered Defense",description:"Improve the protective qualities of crafted shields.",icon:"🛡️",requiredPoints:3,requiredTalents:["out_5"],bonuses:["+10% defense rating on crafted Shields"],unlocked:!1,position:{x:85,y:60}},{id:"out_10",name:"Salvage Operations",description:"Become adept at recovering usable materials from old gear.",icon:"♻️",requiredPoints:4,requiredTalents:["out_6"],bonuses:["Increased chance to recover materials when deconstructing Outfitting items"],unlocked:!1,position:{x:15,y:80}},{id:"out_11",name:"Armor Specialization",description:"Focus your craft on superior personal protection.",icon:"🧥",requiredPoints:4,requiredTalents:["out_7"],bonuses:["Unlock Tier 2 Armor recipes","+5% quality bonus on crafted armor"],unlocked:!1,position:{x:40,y:80}},{id:"out_12",name:"Utility Enhancement",description:"Learn to integrate basic utility features into gear.",icon:"⚙️",requiredPoints:4,requiredTalents:["out_8"],bonuses:["Unlock recipes for gear with minor utility bonuses (e.g., extra pocket space, slightly reduced noise)"],unlocked:!1,position:{x:60,y:80}},{id:"out_13",name:"Aegis Mastery",description:"Master the art of crafting exceptional shields.",icon:"🏆",requiredPoints:4,requiredTalents:["out_9"],bonuses:["Unlock Tier 2 Shield recipes","+5% quality bonus on crafted shields"],unlocked:!1,position:{x:85,y:80}},{id:"out_14",name:"Master Outfitter",description:"Your skill in tailoring and assembly is renowned.",icon:"⭐",requiredPoints:5,requiredTalents:["out_11","out_12"],bonuses:["Small chance to craft Exceptional quality Outfitting items","10% reduced DEX requirement for wearing crafted gear"],unlocked:!1,position:{x:50,y:100}}]},chemistry:{skill:"chemistry",spentPoints:0,talents:[{id:"chem_1",name:"Efficient Mixing",description:"Reduce herb costs for potions by 15%",icon:"🧪",requiredPoints:0,requiredTalents:[],bonuses:["15% reduced herb costs for potions"],unlocked:!1,position:{x:50,y:20}},{id:"chem_2",name:"Potent Brews",description:"Potions you craft are 20% more effective",icon:"💪",requiredPoints:1,requiredTalents:["chem_1"],bonuses:["20% increased potion effectiveness"],unlocked:!1,position:{x:30,y:40}},{id:"chem_3",name:"Extended Duration",description:"Buffs you create last 25% longer",icon:"⏱️",requiredPoints:1,requiredTalents:["chem_1"],bonuses:["25% increased buff duration"],unlocked:!1,position:{x:70,y:40}},{id:"chem_4",name:"Alchemical Transmutation",description:"Convert common materials into rarer ones",icon:"🔄",requiredPoints:2,requiredTalents:["chem_2"],bonuses:["Unlock material transmutation recipes"],unlocked:!1,position:{x:30,y:60}},{id:"chem_5",name:"Toxicology",description:"Craft advanced poisons with special effects",icon:"☠️",requiredPoints:2,requiredTalents:["chem_3"],bonuses:["Access to advanced poison recipes"],unlocked:!1,position:{x:70,y:60}},{id:"chem_6",name:"Grand Alchemist",description:"Chance to create double potions when crafting",icon:"🧙",requiredPoints:3,requiredTalents:["chem_4","chem_5"],bonuses:["15% chance to craft double potions"],unlocked:!1,position:{x:50,y:80}}]},electronics:{skill:"electronics",spentPoints:0,talents:[{id:"elec_1",name:"Circuit Optimization",description:"Reduce crystal shard costs by 15%",icon:"💡",requiredPoints:0,requiredTalents:[],bonuses:["15% reduced crystal shard costs"],unlocked:!1,position:{x:50,y:20}},{id:"elec_2",name:"Power Efficiency",description:"Gadgets you craft use 20% less energy",icon:"🔋",requiredPoints:1,requiredTalents:["elec_1"],bonuses:["20% reduced energy consumption"],unlocked:!1,position:{x:30,y:40}},{id:"elec_3",name:"Enhanced Optics",description:"Scopes and targeting systems have 15% increased accuracy",icon:"🔭",requiredPoints:1,requiredTalents:["elec_1"],bonuses:["15% increased accuracy for optical devices"],unlocked:!1,position:{x:70,y:40}},{id:"elec_4",name:"Overclocking",description:"Increase the power of electronic devices by 25%",icon:"⚡",requiredPoints:2,requiredTalents:["elec_2"],bonuses:["25% increased power for electronic devices"],unlocked:!1,position:{x:30,y:60}},{id:"elec_5",name:"Miniaturization",description:"Electronic devices weigh 30% less",icon:"🔬",requiredPoints:2,requiredTalents:["elec_3"],bonuses:["30% weight reduction for electronic devices"],unlocked:!1,position:{x:70,y:60}},{id:"elec_6",name:"Quantum Engineering",description:"Unlock crafting of advanced technological devices",icon:"🌌",requiredPoints:3,requiredTalents:["elec_4","elec_5"],bonuses:["Access to quantum technology recipes"],unlocked:!1,position:{x:50,y:80}},{id:"elec_7",name:"Signal Boosting",description:"Enhance the range and power of electronic signals",icon:"📡",requiredPoints:2,requiredTalents:["elec_2"],bonuses:["25% increased signal range"],unlocked:!1,position:{x:15,y:55}},{id:"elec_8",name:"AI Integration",description:"Integrate basic AI into your electronic devices",icon:"🤖",requiredPoints:3,requiredTalents:["elec_7"],bonuses:["Devices can perform automated tasks"],unlocked:!1,position:{x:10,y:75}}]}},Q=(s,t,c)=>{if(!c)return!1;const n=N[t],i=c[t];return!(!i||i.talentPoints<=0||n.spentPoints<s.requiredPoints||s.requiredTalents.length>0&&!s.requiredTalents.every(r=>n.talents.find(o=>o.id===r)?.unlocked))},Se=({selectedTalent:s,selectedSkill:t,userSkills:c,isOpen:n,onClose:i})=>{const[a,r]=v.useState(!1);if(!s||!t)return null;const l=()=>{if(!(!s||!t)){r(!0);try{M.success(`Unlocked ${s.name} talent!`)}catch(o){M.error(`Failed to unlock talent: ${o instanceof Error?o.message:"Unknown error"}`)}finally{r(!1)}}};return e.jsx(Te,{open:n,onOpenChange:i,children:e.jsx(Ae,{children:e.jsxs("div",{className:"bg-gray-900 rounded-t-lg p-4 border-t border-x border-gray-700/50 max-h-[80vh] overflow-y-auto",children:[e.jsxs("div",{className:"flex items-center justify-between mb-3",children:[e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx("div",{className:m("size-10 rounded-full border flex items-center justify-center",s.unlocked?"bg-purple-900 border-purple-400":"bg-gray-800 border-gray-600"),children:e.jsx("div",{className:"text-xl",children:s.icon})}),e.jsxs("div",{children:[e.jsx("h4",{className:"text-white font-medium",children:s.name}),e.jsxs("div",{className:"text-xs text-gray-400",children:["Requires ",s.requiredPoints," points spent"]})]})]}),e.jsxs("div",{className:"flex items-center gap-2",children:[!s.unlocked&&Q(s,t,c)&&e.jsx("button",{disabled:a,className:m("px-3 py-1.5 text-white text-sm rounded-sm transition-colors",a?"bg-yellow-700 cursor-not-allowed":"bg-yellow-600 hover:bg-yellow-700"),onClick:l,children:a?"Unlocking...":"Unlock"}),e.jsx("button",{className:"rounded-full p-1 bg-gray-800 hover:bg-gray-700 transition-colors",onClick:i,children:e.jsx(q,{className:"size-5 text-gray-400"})})]})]}),e.jsx("p",{className:"text-sm text-gray-300 mb-3",children:s.description}),e.jsxs("div",{className:"space-y-2",children:[e.jsx("h5",{className:"text-xs font-medium text-gray-400",children:"Bonuses:"}),e.jsx("ul",{className:"space-y-1",children:s.bonuses.map((o,d)=>e.jsxs("li",{className:"flex items-center gap-1 text-sm",children:[e.jsx(H,{className:"size-3 text-purple-400"}),e.jsx("span",{className:s.unlocked?"text-purple-300":"text-gray-400",children:o})]},d))})]}),s.requiredTalents.length>0&&e.jsxs("div",{className:"mt-3 pt-3 border-t border-gray-700/50",children:[e.jsx("h5",{className:"text-xs font-medium text-gray-400 mb-1",children:"Required Talents:"}),e.jsx("div",{className:"flex flex-wrap gap-1",children:s.requiredTalents.map(o=>{const d=N[t].talents.find(p=>p.id===o);return d?e.jsx("div",{className:m("px-2 py-0.5 rounded-sm text-xs",d.unlocked?"bg-purple-900/30 text-purple-300":"bg-gray-800 text-gray-400"),children:d.name},o):null})})]})]})})})},Te=({children:s,...t})=>e.jsx(V,{...t,children:e.jsxs(G,{children:[e.jsx(W,{className:"fixed inset-0 z-50 bg-black/80 data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0"}),s]})}),Ae=({children:s,...t})=>e.jsxs(Y,{...t,className:m("fixed inset-x-0 bottom-0 z-500 mt-24 flex flex-col rounded-t-lg border border-gray-700/50 bg-gray-950","data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0","data-[state=closed]:slide-out-to-bottom data-[state=open]:slide-in-from-bottom","duration-300"),children:[e.jsx("div",{className:"mx-auto w-12 h-1.5 shrink-0 rounded-full bg-gray-700 my-2"}),s]}),Ce=({selectedSkill:s="fabrication"})=>{const{data:t}=$([s]),[c,n]=v.useState(null),[i,a]=v.useState(!1),r=o=>{n(o),a(!0)},l=()=>{a(!1)};return e.jsxs("div",{className:"bg-gray-800/50 rounded-lg p-3 border border-purple-900/30 h-full md:h-[75vh] overflow-hidden flex flex-col",children:[e.jsxs("div",{className:"flex items-center justify-between mb-4",children:[e.jsxs("div",{children:[e.jsxs("h3",{className:"text-white font-semibold",children:[K(s)," Talents"]}),e.jsx("div",{className:"text-sm text-gray-400",children:Pe[s].description})]}),e.jsx("div",{className:"px-2 py-1 bg-yellow-900/30 rounded-md border border-yellow-900/50",children:e.jsxs("span",{className:"text-sm text-yellow-300",children:[e.jsx("span",{className:"font-medium",children:t?.[s]?.talentPoints||0})," Points Available"]})})]}),e.jsxs("div",{className:"relative w-full grow overflow-x-hidden overflow-y-auto bg-gray-900/70 rounded-lg border border-purple-900/30",children:[e.jsx("div",{className:"absolute inset-0 size-full",style:{backgroundImage:"linear-gradient(to right, rgba(128, 90, 213, 0.05) 1px, transparent 1px), linear-gradient(to bottom, rgba(128, 90, 213, 0.05) 1px, transparent 1px)",backgroundSize:"10% 10%"}}),e.jsx("svg",{className:"absolute inset-0 size-full",children:N[s].talents.map(o=>o.requiredTalents.map(d=>{const p=N[s].talents.find(w=>w.id===d);if(!p)return null;const S=p.position.x+"%",T=p.position.y+"%",u=o.position.x+"%",f=o.position.y+"%",b=p.unlocked&&o.unlocked;return e.jsx("line",{x1:S,y1:T,x2:u,y2:f,stroke:b?"#a855f7":"#4b5563",strokeWidth:"2",strokeDasharray:b?"none":"4,4"},`${d}-${o.id}`)}))}),N[s].talents.map(o=>{const d=t?Q(o,s,t):!1,p=c?.id===o.id;return e.jsx("button",{className:m("absolute size-12 -translate-x-1/2 -translate-y-1/2 rounded-full border-2 flex items-center justify-center transition-all",o.unlocked?"bg-purple-900 border-purple-400 text-white":d?"bg-gray-800 border-yellow-500 text-gray-300 hover:bg-gray-700":"bg-gray-800 border-gray-600 text-gray-500 opacity-70",p&&"ring-2 ring-white"),style:{left:o.position.x+"%",top:o.position.y+"%"},onClick:()=>r(o),children:e.jsx("div",{className:"text-xl",children:o.icon})},o.id)})]}),e.jsx(Se,{selectedTalent:c,selectedSkill:s,userSkills:t||{},isOpen:i,onClose:l})]})},Me=4,ze=({onClose:s,successMessage:t}={},c)=>{const n=D();return O(x.talents.equipAbility.mutationOptions({onMutate:async i=>{const a=x.talents.getEquippedAbilities.queryOptions();await n.cancelQueries(a);const r=n.getQueryData(x.talents.getEquippedAbilities.key()),o=(await n.getQueryData(x.talents.getUnlockedTalents.key()))?.talentList?.find(d=>d.talentInfo.id===i.talentId);if(o){const d=r?[...r]:Array(Me).fill(null);d[i.slot-1]=o.talentInfo,n.setQueryData(x.talents.getEquippedAbilities.key(),d)}return{previousData:r}},onError:(i,a,r)=>{r?.previousData&&n.setQueryData(x.talents.getEquippedAbilities.key(),r.previousData),_.error(i.response?.data?.error||"Failed to equip ability")},onSuccess:(i,a)=>{const r=t||`Ability equipped to slot ${a.slot}`;_.success(r),s?.()},onSettled:()=>{const i=x.talents.getEquippedAbilities.queryOptions();n.invalidateQueries(i),n.invalidateQueries({queryKey:x.talents.getUnlockedTalents.key()})},...c}))},U=4,Ee=({onClose:s,successMessage:t}={},c)=>{const n=D();return O(x.talents.unequipAbility.mutationOptions({onMutate:async i=>{const a=x.talents.getEquippedAbilities.queryOptions();await n.cancelQueries(a);const r=n.getQueryData(x.talents.getEquippedAbilities.key()),l=r?[...r]:Array(U).fill(null);if(i.slot<1||i.slot>U)throw new Error(`Invalid slot: ${i.slot}`);return l[i.slot-1]=null,n.setQueryData(x.talents.getEquippedAbilities.key(),l),{previousData:r}},onError:(i,a,r)=>{r?.previousData&&n.setQueryData(x.talents.getEquippedAbilities.key(),r.previousData),_.error(i.response?.data?.error||"Failed to unequip ability")},onSuccess:(i,a)=>{const r=t||`Ability unequipped from slot ${a.slot}`;_.success(r),s?.()},onSettled:()=>{const i=x.talents.getEquippedAbilities.queryOptions();n.invalidateQueries(i),n.invalidateQueries({queryKey:x.talents.getUnlockedTalents.key()})},...c}))};function Le(){const{data:s,isLoading:t}=F(),[c,n]=v.useState(null);return t?e.jsxs("div",{className:"bg-gray-800/50 rounded-lg p-4 border border-purple-900/30",children:[e.jsx("h3",{className:"text-white mb-4 text-lg font-semibold",children:"Equipped Abilities"}),e.jsx("div",{className:"text-center text-gray-400",children:"Loading abilities..."})]}):e.jsxs(e.Fragment,{children:[e.jsxs("div",{className:"bg-gray-800/50 rounded-lg p-4 border border-purple-900/30",children:[e.jsxs("div",{className:"flex items-center justify-between mb-4",children:[e.jsxs("h3",{className:"text-white text-lg font-semibold flex items-center gap-2",children:[e.jsx(P,{className:"size-5 text-purple-400"}),"Equipped Abilities"]}),e.jsx(X,{to:"/abilities",className:"text-purple-400 hover:text-purple-300 text-sm transition-colors",children:"Manage →"})]}),e.jsx("div",{className:"grid grid-cols-2 md:grid-cols-4 gap-3",children:[0,1,2,3].map(i=>{const a=s?.[i];return e.jsx(Ie,{ability:a,slotNumber:i+1,onClick:()=>n(i+1)},i)})}),s?.some(i=>i)&&e.jsx("div",{className:"mt-4 pt-3 border-t border-gray-700",children:e.jsx("div",{className:"text-xs text-gray-400 text-center",children:"Use abilities in battle to gain tactical advantages"})})]}),c&&e.jsx(Ue,{slot:c,currentAbility:s?.[c-1],onClose:()=>n(null)})]})}function Ie({ability:s,slotNumber:t,onClick:c}){if(!s)return e.jsxs("button",{className:"bg-gray-900/50 rounded-lg p-3 border-2 border-dashed border-gray-700 aspect-square flex flex-col items-center justify-center hover:border-purple-700/50 hover:bg-gray-900/70 transition-all cursor-pointer",onClick:c,children:[e.jsxs("div",{className:"text-gray-400 text-xs mb-1",children:["Slot ",t]}),e.jsx("div",{className:"text-gray-500 text-xs text-center",children:"Empty"})]});const i=(s?.name?B[s.name]:void 0)?.image||s?.image;return e.jsxs("button",{className:"bg-gray-900/50 rounded-lg p-2 border border-purple-900/30 hover:border-purple-700/50 transition-colors group cursor-pointer",onClick:c,children:[e.jsxs("div",{className:"text-xs text-gray-400 mb-1 text-center",children:["Slot ",t]}),e.jsxs("div",{className:"aspect-square relative",children:[i?e.jsx("img",{src:i,alt:s.name,className:"w-full h-full object-cover rounded-md"}):e.jsx("div",{className:"w-full h-full bg-purple-900/30 rounded-md flex items-center justify-center",children:e.jsx(P,{className:"size-6 text-purple-400"})}),e.jsx("div",{className:"absolute inset-x-0 bottom-0 bg-black/70 text-white text-xs p-1 rounded-b-md",children:e.jsx("div",{className:"truncate text-center",children:s.displayName})})]}),s.staminaCost&&e.jsx("div",{className:"mt-1 text-center",children:e.jsxs("span",{className:"text-xs text-blue-400",children:[s.staminaCost," Stamina"]})})]})}function Ue({slot:s,onClose:t,currentAbility:c}){const{data:n,isLoading:i}=Z(),{data:a}=F(),{mutate:r,isPending:l}=ze({onClose:t,successMessage:void 0}),{mutate:o,isPending:d}=Ee({onClose:t,successMessage:void 0}),p=n?.talentList?.filter(u=>{const f=u.talentInfo.staminaCost&&u.talentInfo.staminaCost>0,b=a?.some(w=>w?.id===u.talentInfo.id&&w?.id!==c?.id);return f&&!b}),S=u=>{r({talentId:u.talentInfo.id,slot:s})},T=()=>{o({slot:s})};return e.jsx("div",{className:"fixed inset-0 bg-black/50 flex items-center justify-center z-50 p-4",children:e.jsxs("div",{className:"bg-gray-900 rounded-lg max-w-2xl w-full max-h-[80vh] overflow-hidden border border-purple-900/30",children:[e.jsxs("div",{className:"flex items-center justify-between p-4 border-b border-gray-800",children:[e.jsxs("h3",{className:"text-xl font-semibold text-white",children:["Select Ability for Slot ",s]}),e.jsx("button",{className:"text-gray-400 hover:text-white transition-colors",disabled:l,onClick:t,children:e.jsx(q,{className:"size-5"})})]}),e.jsx("div",{className:"p-4 overflow-y-auto max-h-[calc(80vh-80px)]",children:i?e.jsx("div",{className:"text-center text-gray-400 py-8",children:"Loading abilities..."}):!p||p.length===0?e.jsxs("div",{className:"text-center text-gray-400 py-8",children:[e.jsx("p",{children:"No combat abilities unlocked yet."}),e.jsx("p",{className:"text-sm mt-2",children:"Visit the talents page to unlock abilities."})]}):e.jsxs("div",{className:"grid grid-cols-1 sm:grid-cols-2 gap-4",children:[c&&e.jsx("button",{disabled:d,className:"bg-gray-800/50 rounded-lg p-4 border transition-all text-left cursor-pointer border-red-700 hover:bg-gray-800/70 hover:border-red-500",onClick:T,children:e.jsxs("div",{className:"flex gap-3",children:[e.jsx("div",{className:"w-16 h-16 flex-shrink-0 flex items-center justify-center bg-red-900/30 rounded-md",children:e.jsx(q,{className:"size-8 text-red-400"})}),e.jsxs("div",{className:"flex-1",children:[e.jsx("h4",{className:"text-white font-medium mb-1",children:"Unequip Ability"}),e.jsx("p",{className:"text-gray-400 text-sm mb-2",children:"Remove the ability from this slot"})]})]})}),p.map(u=>{const f=c?.id===u.talentInfo.id,b=B[u.talentInfo.name]?.image||u.talentInfo.image;return e.jsx("button",{disabled:l||f,className:m("bg-gray-800/50 rounded-lg p-4 border transition-all text-left cursor-pointer",f?"border-green-700 bg-green-900/20 cursor-not-allowed":"border-gray-700 hover:border-purple-700 hover:bg-gray-800/70"),onClick:()=>S(u),children:e.jsxs("div",{className:"flex gap-3",children:[e.jsx("div",{className:"w-16 h-16 flex-shrink-0",children:b?e.jsx("img",{src:b,alt:u.talentInfo.displayName,className:"w-full h-full object-cover rounded-md"}):e.jsx("div",{className:"w-full h-full bg-purple-900/30 rounded-md flex items-center justify-center",children:e.jsx(P,{className:"size-8 text-purple-400"})})}),e.jsxs("div",{className:"flex-1",children:[e.jsxs("h4",{className:"text-white font-medium mb-1",children:[u.talentInfo.displayName,f&&e.jsx("span",{className:"text-xs text-green-400 ml-2",children:"(Equipped)"})]}),e.jsx("p",{className:"text-gray-400 text-sm mb-2",children:u.talentInfo.description||"No description available"}),e.jsxs("div",{className:"flex items-center gap-4 text-xs",children:[e.jsxs("span",{className:"text-blue-400",children:[u.talentInfo.staminaCost," Stamina"]}),e.jsxs("span",{className:"text-gray-500",children:["Level ",u.level,"/",u.talentInfo.maxPoints]})]})]})]})},u.talentInfo.id)})]})})]})})}const De={novice:"border-gray-500 bg-gray-900/30",standard:"border-green-500 bg-green-900/30",enhanced:"border-blue-500 bg-blue-900/30",specialist:"border-purple-500 bg-purple-900/30",military:"border-orange-500 bg-orange-900/30",legendary:"border-yellow-500 bg-yellow-900/30"};function g({item:s,slotName:t,slot:c,icon:n,className:i,onUnequip:a}){const r=!s,l=ie();return e.jsx("div",{title:s?`${s.name} (Level ${s.level})`:`Empty ${t} slot`,className:m("relative w-16 h-16 rounded-lg border-2 flex items-center justify-center transition-all duration-200 group",r?"border-gray-600 bg-gray-800/50 hover:border-gray-500":De[s.rarity],i),children:r?e.jsx("div",{className:"text-gray-500 opacity-50",children:n}):e.jsxs("div",{className:"relative w-full h-full",children:[e.jsx(ae,{item:s,className:"w-full h-full object-cover rounded-md"}),s.upgradeLevel>0&&e.jsxs("div",{className:"absolute -top-1 -right-1 bg-blue-600 text-white text-xs rounded-full w-4 h-4 flex items-center justify-center font-bold",children:["+",s.upgradeLevel]}),e.jsx("button",{title:`Unequip ${s.name}`,className:m("absolute -top-1 -left-1 bg-red-600 hover:bg-red-700 text-white rounded-full w-4 h-4 flex items-center justify-center transition-opacity duration-200",l?"opacity-100":"opacity-0 group-hover:opacity-100"),onClick:o=>{o.stopPropagation(),a(c)},children:e.jsx(q,{className:"w-2.5 h-2.5"})})]})})}function Oe(){const{data:s,isLoading:t,error:c}=J(),n=ee(),i=a=>{n.mutate({slot:a})};return t?e.jsx("div",{className:"flex items-center justify-center p-8",children:e.jsx("div",{className:"animate-spin rounded-full h-8 w-8 border-b-2 border-purple-500"})}):c?e.jsx("div",{className:"text-red-400 text-center p-4",children:"Failed to load equipped items"}):e.jsxs("div",{className:"space-y-4",children:[e.jsxs("div",{className:"grid grid-cols-3 gap-2 max-w-xs mx-auto",children:[e.jsx("div",{}),e.jsx(g,{item:s?.head||null,slotName:"head",slot:"head",icon:e.jsx(je,{className:"w-6 h-6"}),onUnequip:i}),e.jsx("div",{}),e.jsx(g,{item:s?.ranged||null,slotName:"ranged",slot:"ranged",icon:e.jsx(se,{className:"w-6 h-6"}),onUnequip:i}),e.jsx(g,{item:s?.chest||null,slotName:"chest",slot:"chest",icon:e.jsx(I,{className:"w-6 h-6"}),onUnequip:i}),e.jsx(g,{item:s?.finger||null,slotName:"finger",slot:"finger",icon:e.jsx(he,{className:"w-6 h-6"}),onUnequip:i}),e.jsx(g,{item:s?.weapon||null,slotName:"weapon",slot:"weapon",icon:e.jsx(te,{className:"w-6 h-6"}),onUnequip:i}),e.jsx(g,{item:s?.legs||null,slotName:"legs",slot:"legs",icon:e.jsx(I,{className:"w-6 h-6"}),onUnequip:i}),e.jsx(g,{item:s?.offhand||s?.shield||null,slotName:"offhand/shield",slot:s?.offhand?"offhand":s?.shield?"shield":"offhand",icon:e.jsx(C,{className:"w-6 h-6"}),onUnequip:i}),e.jsx(g,{item:s?.hands||null,slotName:"hands",slot:"hands",icon:e.jsx(qe,{className:"w-6 h-6"}),onUnequip:i}),e.jsx(g,{item:s?.feet||null,slotName:"feet",slot:"feet",icon:e.jsx(R,{className:"w-6 h-6"}),onUnequip:i}),e.jsx("div",{})]}),e.jsxs("div",{className:"mt-4 p-3 bg-gray-900/50 rounded-lg border border-gray-700",children:[e.jsx("h4",{className:"text-white text-sm font-medium mb-2",children:"Total Equipment Bonuses"}),(()=>{if(!s||Object.values(s).filter(Boolean).length===0)return e.jsx("div",{className:"text-gray-400 text-center py-2",children:"No items equipped"});const a={damage:0,armour:0,health:0,energy:0,actionPoints:0,baseAmmo:0},r={};return Object.values(s).forEach(l=>{l&&(l.damage&&(a.damage+=l.damage),l.armour&&(a.armour+=l.armour),l.health&&(a.health+=l.health),l.energy&&(a.energy+=l.energy),l.actionPoints&&(a.actionPoints+=l.actionPoints),l.baseAmmo&&(a.baseAmmo+=l.baseAmmo),l.itemEffects&&l.itemEffects.forEach(o=>{const d=o.effectKey,p=o.effectValue||0;o.effectModifier==="add"?r[d]=(r[d]||0)+p:o.effectModifier==="set"&&(r[d]=Math.max(r[d]||0,p))}))}),e.jsxs("div",{className:"space-y-3",children:[e.jsxs("div",{className:"grid grid-cols-2 gap-2 text-xs",children:[a.damage>0&&e.jsxs("div",{className:"flex justify-between",children:[e.jsx("span",{className:"text-gray-400",children:"Damage:"}),e.jsxs("span",{className:"text-red-400",children:["+",a.damage]})]}),a.armour>0&&e.jsxs("div",{className:"flex justify-between",children:[e.jsx("span",{className:"text-gray-400",children:"Armour:"}),e.jsxs("span",{className:"text-blue-400",children:["+",a.armour]})]}),a.health>0&&e.jsxs("div",{className:"flex justify-between",children:[e.jsx("span",{className:"text-gray-400",children:"Health:"}),e.jsxs("span",{className:"text-green-400",children:["+",a.health]})]}),a.energy>0&&e.jsxs("div",{className:"flex justify-between",children:[e.jsx("span",{className:"text-gray-400",children:"Energy:"}),e.jsxs("span",{className:"text-yellow-400",children:["+",a.energy]})]}),a.actionPoints>0&&e.jsxs("div",{className:"flex justify-between",children:[e.jsx("span",{className:"text-gray-400",children:"Action Points:"}),e.jsxs("span",{className:"text-purple-400",children:["+",a.actionPoints]})]}),a.baseAmmo>0&&e.jsxs("div",{className:"flex justify-between",children:[e.jsx("span",{className:"text-gray-400",children:"Base Ammo:"}),e.jsxs("span",{className:"text-orange-400",children:["+",a.baseAmmo]})]})]}),Object.keys(r).length>0&&e.jsx(e.Fragment,{children:e.jsxs("div",{className:"border-t border-gray-700 pt-2",children:[e.jsx("div",{className:"text-gray-400 text-xs mb-1",children:"Special Effects:"}),e.jsx("div",{className:"grid grid-cols-1 gap-1 text-xs",children:Object.keys(r).map(l=>e.jsxs("div",{className:"flex justify-between",children:[e.jsxs("span",{className:"text-gray-400 capitalize",children:[l.replace(/([A-Z])/g," $1").trim(),":"]}),e.jsxs("span",{className:"text-cyan-400",children:["+",r[l]]})]},l))})]})})]})})()]})]})}function Ze(){const{data:s}=$(),{data:t}=ne(),[c,n]=v.useState(null),[i,a]=v.useState(null),r=t?.xpForNextLevel?t.xp/t.xpForNextLevel*100:0,l=t?.health?t.currentHealth/t.health*100:0,o=t?.energy?Math.min(t.energy/100*100,100):0,d=t?.maxActionPoints?t.actionPoints/t.maxActionPoints*100:0;return c?e.jsxs("div",{className:"max-w-(--breakpoint-md) mx-auto h-full overflow-hidden flex flex-col",children:[e.jsxs("button",{type:"button",className:m("mt-0 mb-2 items-center rounded-md border border-transparent bg-indigo-700 px-6 py-2 text-base text-white shadow-xs hover:bg-indigo-800 focus:outline-hidden focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2 md:inline-flex"),onClick:()=>{n(null)},children:[e.jsx(E,{className:"mr-3 size-5 inline"}),"Back"]}),e.jsx(Ce,{selectedSkill:c})]}):i?e.jsxs("div",{className:"max-w-(--breakpoint-md) mx-auto h-full overflow-hidden flex flex-col",children:[e.jsxs("button",{type:"button",className:m("mt-0 mb-2 items-center rounded-md border border-transparent bg-indigo-700 px-6 py-2 text-base text-white shadow-xs hover:bg-indigo-800 focus:outline-hidden focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2 md:inline-flex"),onClick:()=>{a(null)},children:[e.jsx(E,{className:"mr-3 size-5 inline"}),"Back"]}),e.jsxs("div",{className:"bg-gray-800/50 rounded-lg p-3 border border-purple-900/30 h-full md:h-[75vh] overflow-hidden flex flex-col",children:[e.jsxs("div",{className:"flex items-center justify-between mb-4",children:[e.jsxs("div",{children:[e.jsxs("h3",{className:"text-white font-semibold capitalize",children:[i," Details"]}),e.jsxs("div",{className:"text-sm text-gray-400",children:["View and manage your ",i," skill"]})]}),e.jsx("div",{className:"px-2 py-1 bg-green-900/30 rounded-md border border-green-900/50",children:e.jsxs("span",{className:"text-sm text-green-300",children:["Level"," ",e.jsx("span",{className:"font-medium",children:s?.[i]?.level||0})]})})]}),e.jsx("div",{className:"relative w-full grow overflow-x-hidden overflow-y-auto bg-gray-900/70 rounded-lg border border-green-900/30 p-4",children:e.jsxs("div",{className:"text-white",children:[e.jsxs("p",{className:"text-gray-400 mb-4",children:["This panel would show detailed information about your ",i," skill, including:"]}),e.jsxs("ul",{className:"list-disc pl-5 space-y-2 text-gray-300",children:[e.jsx("li",{children:"Skill bonuses and benefits"}),e.jsx("li",{children:"Available gathering locations"}),e.jsx("li",{children:"Resource types and rarity chances"}),e.jsx("li",{children:"Special abilities unlocked at certain levels"}),e.jsx("li",{children:"Tools and equipment that improve efficiency"})]})]})})]})]}):e.jsxs("div",{className:"h-full overflow-y-auto p-2 max-w-(--breakpoint-2xl) mx-auto",children:[e.jsx("div",{className:"mb-4 bg-gray-800/50 rounded-lg p-3 border border-purple-900/30",children:e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs("div",{className:"flex items-center gap-3",children:[t?.avatar&&e.jsx("img",{src:t.avatar,alt:t.username,className:"size-16 rounded-full border-2 border-purple-500"}),e.jsxs("div",{children:[e.jsx("h2",{className:"text-2xl font-bold text-white",children:t?.username}),e.jsxs("p",{className:"text-gray-400",children:["Level ",t?.level," ",t?.class]})]})]}),e.jsxs("div",{className:"text-right",children:[e.jsxs("div",{className:"flex items-center gap-2 text-yellow-400",children:[e.jsx(ce,{className:"size-4"}),e.jsxs("span",{className:"font-medium",children:["$",t?.cash.toLocaleString()]})]}),e.jsx("div",{className:"flex items-center gap-2 text-green-400 text-sm",children:e.jsxs("span",{children:["Bank: $",t?.bank_balance.toLocaleString()]})})]})]})}),e.jsxs("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-4",children:[e.jsxs("div",{className:"space-y-4",children:[e.jsxs("div",{className:"bg-gray-800/50 rounded-lg p-3 border border-purple-900/30",children:[e.jsxs("h3",{className:"text-white font-semibold mb-3 flex items-center gap-2",children:[e.jsx(re,{className:"size-4 text-purple-400"}),"Character Stats"]}),e.jsxs("div",{className:"space-y-3",children:[e.jsx(k,{label:"Health",current:t?.currentHealth||0,max:t?.health||1,progress:l,color:"bg-red-500",icon:e.jsx(z,{className:"size-4 text-red-400"})}),e.jsx(k,{label:"Energy",current:t?.energy||0,max:100,progress:o,color:"bg-blue-500",icon:e.jsx(xe,{className:"size-4 text-blue-400"})}),e.jsx(k,{label:"Action Points",current:t?.actionPoints||0,max:t?.maxActionPoints||1,progress:d,color:"bg-green-500",icon:e.jsx(de,{className:"size-4 text-green-400"})}),e.jsx(k,{label:"Experience",current:t?.xp||0,max:t?.xpForNextLevel||1,progress:r,color:"bg-purple-500",icon:e.jsx(oe,{className:"size-4 text-purple-400"})})]})]}),e.jsxs("div",{className:"bg-gray-800/50 rounded-lg p-3 border border-purple-900/30",children:[e.jsxs("h3",{className:"text-white font-semibold mb-3 flex items-center gap-2",children:[e.jsx(le,{className:"size-4 text-orange-400"}),"Attributes"]}),e.jsxs("div",{className:"grid grid-cols-2 md:grid-cols-3 gap-3",children:[e.jsx(j,{icon:e.jsx(ue,{className:"size-4 text-orange-400"}),name:"Strength",value:s?.strength.level??0}),e.jsx(j,{icon:e.jsx(C,{className:"size-4 text-blue-400"}),name:"Defence",value:s?.defence.level??0}),e.jsx(j,{icon:e.jsx(R,{className:"size-4 text-green-400"}),name:"Dexterity",value:s?.dexterity.level??0}),e.jsx(j,{icon:e.jsx(me,{className:"size-4 text-purple-400"}),name:"Intelligence",value:s?.intelligence.level??0}),e.jsx(j,{icon:e.jsx(P,{className:"size-4 text-green-400"}),name:"Endurance",value:s?.endurance.level??0}),e.jsx(j,{icon:e.jsx(z,{className:"size-4 text-pink-400"}),name:"Vitality",value:s?.vitality.level??0})]})]}),e.jsx(Le,{})]}),e.jsxs("div",{className:"space-y-4",children:[e.jsxs("div",{className:"bg-gray-800/50 rounded-lg p-3 border border-purple-900/30",children:[e.jsxs("h3",{className:"text-white font-semibold mb-3 flex items-center gap-2",children:[e.jsx(C,{className:"size-4 text-blue-400"}),"Equipment"]}),e.jsx(Oe,{})]}),e.jsxs("div",{className:"bg-gray-800/50 rounded-lg p-3 border border-purple-900/30",children:[e.jsxs("h3",{className:"text-white font-semibold mb-3 flex items-center gap-2",children:[e.jsx(A,{className:"size-4 text-purple-400"}),"Skills"]}),e.jsxs("div",{className:"flex flex-col gap-1 lg:flex-row lg:gap-6",children:[e.jsxs("div",{className:"space-y-2 flex-1 lg:space-y-4",children:[e.jsx("div",{className:"mb-2 lg:mb-0",children:e.jsxs("h4",{className:"text-green-300 text-sm mb-2 flex items-center",children:[e.jsx(L,{className:"size-4 mr-1"})," Gathering"]})}),e.jsx(y,{name:"Mining",skill:s?.mining,icon:e.jsx(L,{className:"size-4 text-gray-400"}),onClick:()=>a("mining")}),e.jsx(y,{name:"Scavenging",skill:s?.scavenging,icon:e.jsx(Fe,{className:"size-4 text-green-400"}),onClick:()=>a("scavenging")}),e.jsx(y,{name:"Foraging",skill:s?.foraging,icon:e.jsx($e,{className:"size-4 text-emerald-400"}),onClick:()=>a("foraging")})]}),e.jsxs("div",{className:"space-y-2 flex-1 lg:space-y-4",children:[e.jsx("div",{className:"border-t border-gray-700 pt-2 mt-4 lg:border-t-0 lg:mt-0 lg:pt-0",children:e.jsxs("h4",{className:"text-purple-300 text-sm mb-2 flex items-center",children:[e.jsx(A,{className:"size-4 mr-1"})," Crafting"]})}),e.jsx(y,{name:"Fabrication",skill:s?.fabrication,icon:e.jsx(A,{className:"size-4 text-orange-400"}),onClick:()=>n("fabrication")}),e.jsx(y,{name:"Outfitting",skill:s?.outfitting,icon:e.jsx(Be,{className:"size-4 text-blue-400"}),onClick:()=>n("outfitting")}),e.jsx(y,{name:"Chemistry",skill:s?.chemistry,icon:e.jsx(Ne,{className:"size-4 text-purple-400"}),onClick:()=>n("chemistry")}),e.jsx(y,{name:"Electronics",skill:s?.electronics,icon:e.jsx(be,{className:"size-4 text-cyan-400"}),onClick:()=>n("electronics")})]})]})]})]})]})]})}function k({label:s,current:t,max:c,progress:n,color:i,icon:a}){return e.jsxs("div",{className:"space-y-1",children:[e.jsxs("div",{className:"flex items-center justify-between text-sm",children:[e.jsxs("span",{className:"text-gray-400 flex items-center gap-1",children:[a,s]}),e.jsxs("span",{className:"text-white",children:[t.toLocaleString()," / ",c.toLocaleString()]})]}),e.jsx("div",{className:"w-full h-2 bg-gray-700 rounded-full overflow-hidden",children:e.jsx("div",{style:{width:`${Math.min(n,100)}%`},className:m("h-full rounded-full transition-all duration-300",i)})})]})}function j({icon:s,name:t,value:c}){return e.jsx("div",{className:"bg-gray-900/50 rounded-lg p-2 border border-gray-700",children:e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx("div",{className:"size-6 rounded-full bg-gray-700 flex items-center justify-center flex-shrink-0",children:s}),e.jsxs("div",{className:"min-w-0",children:[e.jsx("p",{className:"text-gray-400 text-xs truncate",children:t}),e.jsx("p",{className:"text-white text-sm font-medium",children:c})]})]})})}function y({name:s,skill:t,icon:c,onClick:n}){const i=t?.experience/t?.expToNextLevel*100||0,a=["fabrication","outfitting","chemistry","electronics"].includes(s.toLowerCase()),r=["mining","scavenging","foraging"].includes(s.toLowerCase()),l=t?.talentPoints>0;return e.jsxs("div",{role:n?"button":void 0,tabIndex:n?0:void 0,"aria-label":n?`View ${s} skill details`:void 0,className:m("bg-gray-900/50 rounded-lg p-2 relative",n&&"hover:bg-gray-800/70 transition-colors cursor-pointer",a&&n&&"border border-gray-700 hover:border-purple-500",r&&n&&"border border-gray-700 hover:border-green-500",l&&"from-yellow-950/20 to-transparent bg-linear-to-r"),onClick:n,onKeyDown:o=>{n&&(o.key==="Enter"||o.key===" ")&&(o.preventDefault(),n())},children:[l&&e.jsxs("div",{className:"absolute -top-1 -right-1 flex size-3",children:[e.jsx("span",{className:"animate-ping absolute inline-flex size-full rounded-full bg-yellow-400 opacity-75"}),e.jsx("span",{className:"relative inline-flex rounded-full size-3 bg-yellow-500"})]}),e.jsxs("div",{className:"flex justify-between items-start mb-1",children:[e.jsxs("div",{className:"flex items-center gap-2",children:[e.jsx("div",{className:m("size-7 rounded-full flex items-center justify-center",a?"bg-gray-800":r?"bg-gray-800/80":"bg-gray-800"),children:c}),e.jsxs("div",{children:[e.jsx("h4",{className:"text-white text-sm font-medium",children:s}),e.jsx("div",{className:"flex items-center gap-1 text-xs",children:e.jsxs("span",{className:m(a?"text-purple-300":r?"text-green-300":"text-purple-300"),children:["Level ",t?.level]})})]})]}),e.jsxs("span",{className:"text-gray-300 text-xs absolute bottom-3 left-3",children:[t?.experience?.toLocaleString(),"/",t?.expToNextLevel?.toLocaleString()," XP"]})]}),e.jsx("div",{className:"w-full h-1.5 bg-gray-800 rounded-full overflow-hidden",children:e.jsx("div",{style:{width:`${i}%`},className:m("h-full rounded-full",a?"bg-purple-600":r?"bg-green-600":"bg-purple-600")})}),t?.talentPoints>0&&e.jsx("div",{className:"absolute top-2 right-3 bg-yellow-900/70 px-2 py-0 flex items-center justify-center rounded-full border border-yellow-500/50",children:e.jsxs("span",{className:"text-xs text-yellow-300 font-medium",children:[t.talentPoints," ",t.talentPoints===1?"point":"points"]})}),n&&e.jsxs("div",{className:m("mt-2 text-xs text-right"),children:[l?e.jsx("span",{className:"text-yellow-300",children:"Talent points available!"}):e.jsx("span",{className:m(r?"text-green-400":"text-purple-400"),children:"View talent tree"}),e.jsx("span",{className:m("ml-1",r?"text-green-400":"text-purple-400",l&&"text-yellow-300"),children:"→"})]})]})}function Fe({className:s}){return e.jsxs("svg",{xmlns:"http://www.w3.org/2000/svg",width:"24",height:"24",viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round",className:s,children:[e.jsx("circle",{cx:"11",cy:"11",r:"8"}),e.jsx("path",{d:"m21 21-4.3-4.3"})]})}function $e({className:s}){return e.jsxs("svg",{xmlns:"http://www.w3.org/2000/svg",width:"24",height:"24",viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round",className:s,children:[e.jsx("path",{d:"M11 20A7 7 0 0 1 9.8 6.1C15.5 5 17 4.48 19 2c1 2 2 4.18 2 8 0 5.5-4.78 10-10 10Z"}),e.jsx("path",{d:"M2 21c0-3 1.85-5.36 5.08-6C9.5 14.52 12 13 13 12"})]})}function Be({className:s}){return e.jsx("svg",{xmlns:"http://www.w3.org/2000/svg",width:"24",height:"24",viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round",className:s,children:e.jsx("path",{d:"M20.38 3.46 16 2a4 4 0 0 1-8 0L3.62 3.46a2 2 0 0 0-1.34 2.23l.58 3.47a1 1 0 0 0 .99.84H6v10c0 1.1.9 2 2 2h8a2 2 0 0 0 2-2V10h2.15a1 1 0 0 0 .99-.84l.58-3.47a2 2 0 0 0-1.34-2.23z"})})}export{Ze as default};
