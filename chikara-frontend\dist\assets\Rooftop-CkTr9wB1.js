import{e as y,g,c as l,y as p,a as b,v as N,B as j,r as v,j as e,h as w,d2 as R,ae as C,C as k,an as n,b as B,t as D,L}from"./index-DOR_A_Wy.js";import{A as P}from"./ag-theme-quartz-B3qFNd3q.js";const A=()=>{const t=y();return g(l.battle.battleBeginRooftopBattle.mutationOptions({onSuccess:()=>{t.invalidateQueries({queryKey:l.battle.status.key()}),t.invalidateQueries({queryKey:l.rooftop.npcList.key()}),t.invalidateQueries({queryKey:p.USER.CURRENTUSERINFO})}}))},O=t=>{const{value:a}=t;return a?e.jsxs("div",{className:"relative flex size-full p-5 md:p-3",children:[e.jsx(C,{item:a,className:"mx-auto",height:"h-full"}),t.data?.itemRewardQuantity>1&&e.jsxs("p",{className:"-translate-x-1/2 absolute bottom-4 left-1/2 rounded-lg bg-black/25 px-1 text-center font-bold text-custom-yellow text-sm leading-0 md:bottom-1.5",children:["x",t.data?.itemRewardQuantity]})]}):e.jsx("p",{className:"mt-6 text-bold text-gray-400 text-lg",children:"?"})},S=t=>{const{value:a}=t;return e.jsxs("div",{className:w(t.data?.defeated&&"grayscale",t.data?.disabled&&"opacity-25 grayscale","relative flex h-full flex-col items-center justify-center py-0.5 md:w-full md:flex-row md:items-start md:justify-normal md:gap-4 md:p-2"),children:[e.jsx(R,{src:t.data.image,className:"size-14 rounded-lg border border-blue-800 md:h-full md:w-auto"}),e.jsx("p",{className:"text-wrap! text-center! text-xs! 2xl:text-base! font-semibold text-blue-400 md:my-auto",children:a}),e.jsxs("p",{className:"text-custom-yellow text-xs md:hidden",children:[t.data.rank," Rank"]})]})},E=({npcList:t,currentUser:a})=>{const r=b(),d=N(),{ROOFTOP_BATTLE_AP_COST:o}=j(),c=A(),m=async s=>{if(a?.actionPoints<o)return n.error("Not enough AP");try{await c.mutateAsync({battleOpponentId:parseInt(s)}),r("/fight")}catch(h){const i=h.message||"Unknown error occurred";console.error(i),n.error(i)}},u=s=>e.jsx("div",{className:"flex size-full items-center justify-center",children:e.jsx(k,{disabled:s.data?.defeated||s.data?.disabled,className:"text-xs! md:text-sm! w-[90%] 2xl:w-1/2",type:"danger",onClick:()=>m(s.data.id),children:s.data?.defeated?"Defeated":e.jsxs("div",{className:"flex flex-col",children:[e.jsx("p",{className:"md:text-sm!",children:"Attack"}),e.jsxs("p",{className:"text-xs! md:-mt-1 mb-1",children:[o," AP"]})]})})}),[x,H]=v.useState([{headerName:"NPC",field:"name",cellRenderer:S},{headerName:"Rank",field:"rank",hide:d,cellClass:"mt-4 text-xl font-semibold text-custom-yellow"},{headerName:"Level",field:"level",wrapHeaderText:!0,autoHeaderHeight:!0,cellClass:"mt-5 text-lg font-bold text-red-500 !md:px-0",maxWidth:d?67:null},{headerName:"Reward",field:"item",cellRenderer:O,sortable:!1},{headerName:"",field:"static",cellRenderer:u,sortable:!1}]),f={flex:1,sortable:!0,suppressMovable:!0,filter:!1,resizable:!1,cellClass:"px-0.5! md:px-2! 2xl:px-6!"};return e.jsx("div",{className:"ag-theme-quartz-dark",style:{width:"100%",overflow:"auto"},children:e.jsx(P,{suppressCellFocus:!0,suppressRowHoverHighlight:!0,rowData:t,columnDefs:x,defaultColDef:f,domLayout:"autoHeight",rowHeight:d?100:80})})};function U(){const{data:t,isLoading:a}=B(l.rooftop.npcList.queryOptions()),{data:r}=D();return e.jsx("section",{className:" mx-auto rounded-lg py-6 md:max-w-7xl",children:e.jsx("div",{className:" mx-auto h-full px-0 md:px-6",children:e.jsx("div",{className:"mx-auto h-full text-center",children:e.jsx("div",{className:"mt-4 w-full md:mx-0",children:e.jsx(L,{isLoading:a,children:e.jsx(E,{npcList:t,currentUser:r})})})})})})}export{U as default};
