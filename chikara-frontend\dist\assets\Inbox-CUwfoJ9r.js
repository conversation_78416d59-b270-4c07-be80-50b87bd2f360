import{u as E,a as v,b as L,c as p,j as e,D as w,d as k,f as N,e as _,g as D,r as y,h as g,R as z,U as M,i as F,k as O,l as I,m as q,n as R,A as B,E as K,s as Q,L as $,o as A,p as G,z as W,q as V,t as Y,v as H,S as J,w as C}from"./index-DOR_A_Wy.js";import{A as U}from"./arrow-left-CbJy6pIq.js";import{l as b}from"./menu-B6tMgF3M.js";function X({sender:s,currentConvoId:n,currentUserId:r}){const d=s?.messages[0].senderId===r,t=Number.parseInt(s?.senderId),{data:i,isLoading:h,error:c}=E(t,{enabled:!!t}),m=v(),{data:x}=L(p.messaging.history.queryOptions()),u=x.filter(f=>f.senderId===t||f.receiverId===t).filter(f=>f.message!=="<NewConversation>")[0].message,l=f=>{m(`/inbox/${f}`)};if(h)return null;if(c)return"An error has occurred: "+c.message;const j=(f,P)=>Number.parseInt(s?.senderId)===Number.parseInt(P)?"bg-slate-800 md:bg-slate-900 brightness-[1.3]":f===!1&&!d?"bg-blue-950":"bg-slate-800 md:bg-slate-900 hover:brightness-[1.2]";return e.jsxs("li",{className:`relative flex h-20 flex-row gap-4 px-4 py-2 ${j(s.messages[0].read,Number.parseInt(n))}`,children:[e.jsx(w,{className:"my-auto h-12 w-auto rounded-full",src:i}),e.jsxs("div",{className:"my-auto w-5/6 md:my-0",children:[e.jsxs("div",{className:"flex justify-between space-x-3",children:[e.jsx("div",{className:"min-w-0 flex-1",children:e.jsxs("button",{className:"block focus:outline-hidden",onClick:()=>l(i?.id),children:[e.jsx("span",{className:"absolute inset-0","aria-hidden":"true"}),e.jsx("p",{className:k("truncate text-sm text-stroke-sm",i.userType==="admin"?"text-red-500":"text-blue-500"),children:i.username})]})}),e.jsx("time",{dateTime:s.messages[0].createdAt,className:"shrink-0 whitespace-nowrap text-blue-300 text-xs",children:N(new Date(s.messages[0].createdAt),"p")})]}),e.jsx("div",{className:"mt-1 font-medium font-body",children:e.jsx("p",{className:k(i.userType==="admin"?"text-red-300":"text-gray-100","line-clamp-2 text-sm"),children:u})})]})]},t)}function T({message:s,type:n="sentMessage",convoUserInfo:r,currentUser:d}){const t=_(),i=n==="sentMessage",h=v(),{mutate:c}=D(p.messaging.readMessage.mutationOptions({onSuccess:()=>{t.invalidateQueries({queryKey:p.messaging.history.key()}),t.invalidateQueries({queryKey:p.messaging.unread.key()})},onError:x=>{console.error("Failed to mark message as read:",x)}}));if(y.useEffect(()=>{s?.read===!1&&c({messageId:s.id})},[s?.read,c]),!s)return null;const m=s.isGlobal&&r.userType==="admin";return e.jsxs("li",{className:g("relative flex min-w-40 gap-5 py-2 md:px-3",i?"flex-row-reverse self-end":"flex-row self-start ",s?.message?.length<6&&"text-center"),children:[e.jsx(w,{className:"my-auto h-12 w-auto cursor-pointer rounded-full",src:i?d:r,onClick:()=>h(`/profile/${i?d?.id:r?.id}`)}),e.jsxs("div",{className:g("group relative flex size-full min-h-14 flex-col justify-start rounded-lg border px-3 pt-2 pb-5 shadow-xl",i?"border-indigo-500 bg-indigo-800":"border-gray-500 bg-gray-700",m&&"bg-blue-800! border-blue-500!"),children:[s.isGlobal&&e.jsx("div",{className:g(r.userType==="admin"?"-top-7 text-red-500 text-sm":"-top-5 text-blue-500 text-xs","-translate-x-1/2 absolute right-[40%]"),children:r.userType==="admin"?"Announcement":"Megaphone"}),e.jsx("div",{className:g(i?"message_arrow_right":"message_arrow_left")}),e.jsx("div",{className:g(m?"text-red-200":"text-gray-50","whitespace-pre-wrap text-sm"),children:e.jsx(z,{msg:s})}),e.jsx("div",{className:g("-bottom-[1.1rem] absolute",i?"right-1 text-right":"left-1 text-left"),children:e.jsxs("p",{"data-tooltip-id":"date-tooltip","data-tooltip-content":N(new M(s.createdAt),"PP, p"),className:"cursor-pointer whitespace-nowrap font-body font-semibold text-slate-300 text-stroke-0 text-xs sm:mt-0",children:[e.jsx("time",{className:"hidden group-hover:block",dateTime:s.createdAt,children:N(new M(s.createdAt),"PP, p")}),e.jsxs("time",{className:"group-hover:hidden",dateTime:s.createdAt,children:[F(s.createdAt)," ago"]})]})})]})]},s.id)}function Z({userId:s,currentUser:n}){const[r,d]=y.useState(""),[t,i]=y.useState(!1),h=_(),[c,m]=y.useState(!1),x=O(()=>{m(!1)}),o=D(p.messaging.sendMessage.mutationOptions({onSuccess:()=>{d(""),h.invalidateQueries({queryKey:p.messaging.history.key()})},onError:l=>{console.error("Sending message failed:",l),I.error(l.message||"Failed to send message")}})),a=()=>{r.length>0?o.mutate({userId:s,message:r}):(i(!0),I.error("Message can't be blank!"),setTimeout(()=>i(!1),2e3))},u=l=>{l.key==="Enter"&&(l.preventDefault(),a())};return e.jsx("div",{className:g(t?"chatMessageBoxError":"chatMessageBox","-translate-x-1/2 absolute bottom-0 left-1/2 mb-4 flex w-[96%] justify-between rounded-md text-center text-sm shadow-xs md:my-3 md:w-5/6 dark:bg-[#15121C]"),children:e.jsxs("div",{className:"flex w-full flex-row",children:[e.jsx("textarea",{required:!0,id:"privateMessage",name:"privateMessage",maxLength:n?.userType==="admin"?2e3:200,rows:2,cols:1,className:"chatTextArea scrollbar h-16 whitespace-pre-wrap rounded-md border-none bg-[#dee2e9] p-2 font-mono text-sm md:h-auto dark:bg-[#15121C] dark:text-white",placeholder:"Your message...",value:r,onChange:l=>d(l.target.value),onKeyDown:l=>u(l)}),e.jsx("div",{className:"relative mx-1 my-auto ml-auto",onClick:l=>{l.stopPropagation(),m(j=>!j)},children:e.jsx(S,{children:e.jsx("img",{src:R,alt:"",className:"size-6 fill-white text-white"})})}),e.jsx(B,{children:c&&e.jsx(K,{userMessage:r,setUserMessage:d,innerRef:x})}),e.jsx("div",{className:"relative my-auto mr-2 ml-1",onClick:a,children:e.jsx(S,{children:e.jsx("img",{src:Q,alt:"",className:"h-5 w-4 fill-white text-white"})})})]})})}const S=q.memo(({children:s})=>e.jsx("button",{className:"size-9 cursor-pointer rounded-md border-[#1F1F2D] border-b bg-[#28287c] shadow-[0_1px_0_0_#303045_inset,0_2px_2px_0_rgba(0,0,0,0.25)] transition hover:brightness-110",children:e.jsx("div",{className:"flex items-center justify-center",children:s})}));function ee({sortedArray:s,sentMessages:n,currentUser:r,convoId:d}){const{data:t,isLoading:i}=E(d,{enabled:!!d}),h=v();n==null&&(n=[]);const c=n.filter(o=>o.receiverId===Number.parseInt(t?.id)),m=s.filter(o=>o.senderId===d),x=c.concat(m[0]?.messages);return x.sort((o,a)=>o.createdAt<a.createdAt?1:-1),e.jsx("section",{"aria-labelledby":"message-heading",className:"flex h-full min-w-0 flex-1 flex-col overflow-hidden",children:d?e.jsx($,{size:14,isLoading:i,children:e.jsxs("div",{className:"relative flex min-h-0 flex-1 flex-col",children:[e.jsx("div",{className:"bg-gray-800 pt-2 pb-3 shadow-sm",children:e.jsxs("div",{className:"flex items-baseline px-4 sm:px-6 lg:px-8",children:[e.jsx(U,{className:"my-auto mr-5 size-7 cursor-pointer md:hidden text-gray-200",onClick:()=>h("/inbox")}),e.jsx(A,{to:`/profile/${t?.id}`,children:e.jsx("div",{className:"my-auto mr-3",children:e.jsx(w,{className:"w-10 rounded-full md:w-14",src:t})})}),e.jsxs("div",{className:"my-auto sm:w-0 sm:flex-1",children:[e.jsx(A,{to:`/profile/${t?.id}`,children:e.jsxs("h1",{id:"message-heading",className:"font-medium text-custom-yellow text-sm md:text-lg",children:[t?.username," ",e.jsxs("span",{className:"text-gray-400 text-sm",children:["#",t?.id]})]})}),e.jsx("p",{className:"mt-1 truncate text-red-400 text-xs",children:t?.gang?.name||"No Gang"})]}),e.jsx(se,{convoUserInfo:t})]})}),e.jsx("ul",{className:"flex flex-1 flex-col-reverse gap-4 overflow-y-auto p-4 pb-16 font-body md:pb-14 lg:px-8",children:x.map(o=>o?.senderId===r?.id?e.jsx(T,{type:"sentMessage",message:o,convoUserInfo:t,currentUser:r},o?.id):e.jsx(T,{type:"receivedMessage",message:o,convoUserInfo:t,currentUser:r},o?.id))}),e.jsx("div",{className:"h-10 w-full border-gray-600 border-t bg-gray-700 ",children:e.jsx(Z,{userId:t?.id,currentUser:r})})]})}):e.jsx("div",{className:"flex size-full",children:e.jsx("div",{className:"m-auto text-2xl text-gray-300",children:"Select a message"})})})}const se=({convoUserInfo:s})=>s?e.jsxs(b,{as:"div",className:"ml-3 inline-block text-left",children:[e.jsx("div",{children:e.jsxs(b.Button,{className:"absolute top-3 right-5 rounded-full p-2 text-gray-400 hover:text-gray-600 focus:outline-hidden focus:ring-2 focus:ring-blue-600 md:top-5",children:[e.jsx("span",{className:"sr-only",children:"Open options"}),e.jsx(G,{className:"size-5","aria-hidden":"true"})]})}),e.jsx(W,{as:y.Fragment,enter:"transition ease-out duration-100",enterFrom:"transform opacity-0 scale-95",enterTo:"transform opacity-100 scale-100",leave:"transition ease-in duration-75",leaveFrom:"transform opacity-100 scale-100",leaveTo:"transform opacity-0 scale-95",children:e.jsx(b.Items,{className:"absolute right-0 z-150 mt-2 w-56 origin-top-right rounded-md border border-gray-400 bg-gray-900 shadow-lg ring-1 ring-black/5 focus:outline-hidden",children:e.jsxs("div",{className:"py-1",children:[e.jsx(b.Item,{children:({active:n})=>e.jsx("button",{type:"button",className:g(n?"bg-gray-700 text-gray-200":"text-gray-200","flex w-full justify-between px-4 py-2 text-sm"),children:e.jsxs("span",{children:["Block ",s.username]})})}),e.jsx(b.Item,{children:({active:n})=>e.jsx("a",{href:"#",className:g(n?"bg-gray-700 text-gray-200":"text-gray-200","flex justify-between px-4 py-2 text-sm"),children:e.jsxs("span",{children:["Report ",s.username]})})}),e.jsx(b.Item,{children:({active:n})=>e.jsx("a",{href:"#",className:g(n?"bg-gray-700 text-gray-200":"text-gray-200","flex justify-between px-4 py-2 text-sm"),children:e.jsx("span",{children:"Delete conversation"})})})]})})})]}):null;function ie(){const{id:s}=V(),{isLoading:n,error:r,data:d}=L(p.messaging.history.queryOptions()),{data:t,isLoading:i}=Y(),h=H(),c=d?.reduce((a,u)=>{const l=u.senderId===t?.id?u.receiverId:u.senderId;return a[l]||(a[l]=[]),a[l].push(u),a},{}),m=[];for(const a in c)m.push({senderId:a===t?.id?c[a][0].receiverId:a,messages:c[a],unreadTotal:c[a].filter(u=>u.read===!1).length});const x=m.sort((a,u)=>a.messages[0].createdAt<u.messages[0].createdAt?1:-1),o=x.filter(a=>Number.parseInt(a.senderId)===t?.id)||[];return n||i?e.jsx(J,{center:!0}):r?"An error has occurred: "+r.message:e.jsxs("div",{className:"fixed flex h-[calc(100dvh-8.75rem)] w-full flex-col md:static md:h-[65dvh] -m-2",children:[e.jsxs("div",{className:g("relative flex h-12 gap-3 border-gray-900 border-y-2 bg-[#343549] p-1 px-4 pb-1.5 md:hidden md:rounded-t-lg"),children:[e.jsx("div",{className:"relative my-auto h-6 w-auto",children:e.jsx("img",{className:"h-6 w-auto",src:C,alt:""})}),e.jsx("h1",{className:"my-auto font-medium text-lg text-stroke-s-sm text-white",children:"Inbox"}),e.jsx("div",{className:"absolute bottom-0 left-0 h-1 w-full bg-[#272839]"})]}),e.jsx("div",{className:"flex min-h-0 flex-1 overflow-hidden",children:!s&&d.length===0?e.jsx("h2",{className:"mx-auto mt-12 text-center text-2xl dark:text-shadow-lg dark:text-slate-200",children:"No Messages to show!"}):e.jsxs("main",{className:"min-w-0 flex-1 border-gray-600 md:border xl:flex",children:[h&&!s?null:e.jsx(ee,{sortedArray:x,sentMessages:o[0]?.messages,currentUser:t,convoId:s}),e.jsx("aside",{className:"xl:order-first xl:block xl:shrink-0",children:e.jsxs("div",{className:"relative flex h-full flex-col border-gray-600 bg-gray-800 md:w-64 md:border-r 2xl:w-96",children:[e.jsx("div",{className:"shrink-0",children:e.jsx("div",{className:"hidden h-14 flex-col justify-center bg-gray-900 px-6 text-stroke-sm md:flex",children:e.jsxs("div",{className:"flex items-baseline space-x-3",children:[e.jsx("img",{className:"my-auto h-6 w-auto",src:C,alt:""}),e.jsx("h2",{className:"font-medium text-gray-200 text-xl",children:"Inbox"})]})})}),e.jsx("nav",{"aria-label":"Message list",className:"min-h-0 flex-1 overflow-y-auto",children:e.jsx("ul",{className:"divide-y divide-gray-600 border-gray-600 border-y",children:x.map(a=>e.jsx(y.Fragment,{children:e.jsx(X,{sender:a,currentConvoId:s,currentUserId:t?.id})},a.senderId))})})]})})]})})]})}export{ie as default};
